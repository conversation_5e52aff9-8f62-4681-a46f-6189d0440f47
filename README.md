# Welcome to One Way Express Shipment App

A mobile application for the Driver to track driver gps every 5min tracking using realtime database for example supabase.

## Feature
The mobile application is using expo framework.
As per the implementation: The mobile application should check Driver location everytime the shipment is not yet complete. And should be done even when the application is iddle in background, this is the key feature of this mobile application. While checking the Driver location then update the database accordingly. here is example of objects structure. Shipment type and Tracking: 


Shipment={id:"ship_123",proId:"PRO123",clientProId:"CLIENT_PRO123",lookupKeyword:"ELECTRONICS_BATCH",itemType:"Electronics",quantity:5,description:"Laptopcomputers",notes:"Handlewithcare",status:ShipmentStatusType.IN_TRANSIT,measurement:{weight:{w:25,unity:{name:"Kilogram",short:"kg"}},dimension:{l:60,w:40,h:20,unity:{name:"Centimeter",short:"cm"}}},client:{id:"client_456",name:"TechSolutionsInc"},routes:[{order:1,shipper:{name:"MainWarehouse",address:{fullAddress:"123WarehouseSt,Chicago,IL",suite:"Unit5",coordinate:{lat:41.8781,lng:-87.6298}},date:"2023-11-01T10:00:00Z",status:"ACCEPTED",lastTrackingChangeDate:"2023-11-01T10:30:00Z"},receiver:{name:"DistributionCenter",address:{fullAddress:"456CenterAve,Detroit,MI",coordinate:{lat:42.3314,lng:-83.0458}},date:"2023-11-03T15:00:00Z",status:"NONE"},status:"PENDING",lastTrackingChangeDate:"2023-11-01T10:30:00Z"}],broker:"FastShippingLLC",finance:{purchaseAmount:15000},updatedAt:"2023-11-01T10:30:00Z",updatedName:"JohnDoe",updatedBy:"user_789"}
trackingExample={"id":"track_123456","updatedAt":"2023-11-15T10:30:00Z","updatedName":"JohnDoe","updatedBy":"user_789","load":"load_456","accountId":"acc_789","shipment":{"id":"ship_123","status":"in_transit"},"latestLocation":{"lat":34.0522,"lng":-118.2437},"routes":[{"type":"PICKUP","address":"123StartSt,LA","lat":34.0522,"lng":-118.2437,"date":"2023-11-15T09:00:00Z"}],"activities":[{"title":"PickupStarted","status":"PICKUP_STARTED","description":"Driverarrivedatpickuplocation","date":"2023-11-15T09:00:00Z","userName":"JohnDriver","device":"MobileApp","isFromMobile":true,"location":{"type":"PICKUP","lat":34.0522,"lng":-118.2437}}],"driverTracks":[{"latitude":34.0522,"longitude":-118.2437,"date":"2023-11-15T09:00:00Z","index":1}]}