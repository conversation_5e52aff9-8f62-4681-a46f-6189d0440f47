import { MarketLoad } from '../models/load-market.model';
import { Tracking } from '../models/tracking.model';
import { flushCache, getFromCache } from '@utils/app.settings';
import { ShipmentService } from './shipment.service';
import * as Sentry from '@sentry/react-native';
import { StorageKeys } from '../models/common';

export class AuthnService {
  static signOut = async (load: MarketLoad): Promise<void> => {
    const svcShipment = new ShipmentService();
    const tracking = await getFromCache<Tracking>(StorageKeys.TRACKING);

    if (!tracking?.id) {
      console.log('no tracking found -- terrible');
      return;
    }
    
    await svcShipment
      .trace(`Signed Out. Driver just logged out.`, load, tracking)
      .finally(async () => {
        await flushCache();
      });
  };

  static signIn = async (
    phone: string
  ): Promise<{
    load?: MarketLoad;
    tracking?: Tracking;
    error?: string;
  }> => {
    try {
      const svcShipment = new ShipmentService();
      flushCache();

      const { data, tracking, error } = await svcShipment.get(phone);

      if (error) {
        Sentry.captureMessage(`Phone Number: ${phone} ` + error);
        console.log(error);
        return { error: 'Sign failed, something went wrong.' };
      }

      if (!data) {
        return { error: `The information does not match ours records.` };
      }

      if (data.driver?.phone?.toString() !== phone.toString()) {
        Sentry.captureMessage(
          `ProID: ${data.shipment.proId} Couldn't find any Tracking information. Please contact support.`
        );
        return { error: `The information does not match ours records.` };
      }
      if (!tracking?.id) {
        Sentry.captureMessage(
          `ProID: ${data.shipment.proId} Couldn't find any Tracking information. Please contact support.`
        );
        return { error: `The information does not match ours records.` };
      }
      return { load: data, tracking };
    } catch (error) {
      Sentry.captureException(error, {
        extra: {
          message: 'Unable to pickup shipment',
        },
      });
      return { error: 'login error: something went wrong.' };
    }
  };
}
