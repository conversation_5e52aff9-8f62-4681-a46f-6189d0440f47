{"name": "@babel/helpers", "version": "7.26.7", "description": "Collection of helper functions used by Babel transforms.", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-helpers", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helpers"}, "main": "./lib/index.js", "dependencies": {"@babel/template": "^7.25.9", "@babel/types": "^7.26.7"}, "devDependencies": {"@babel/generator": "^7.26.5", "@babel/helper-plugin-test-runner": "^7.25.9", "@babel/parser": "^7.26.7", "regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}