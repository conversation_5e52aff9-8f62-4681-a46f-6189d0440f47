import { useEffect, useState } from "react";
import { MarketLoad } from "@core/models/load-market.model";
import { MessageItem, Messaging } from "@core/models/tracking.model";
import { MessagingService } from "@core/services/messaging.service";
import { Dimensions, Pressable, View } from "react-native";
import { GiftedChat, IMessage } from "react-native-gifted-chat";
import * as Sentry from "@sentry/react-native";
import { generateUUID } from "@utils/app.settings";
import { Dialog, Icon, MD3Colors, Portal, Text } from "react-native-paper";

export default function ChatView({
  load,
  liveMessage,
  show,
  close,
}: {
  show: boolean;
  load: MarketLoad;
  liveMessage?: Messaging;
  close: () => void;
}) {
  const [data, setData] = useState<Messaging | undefined>(undefined);
  const [messages, setMessages] = useState<IMessage[]>([]);
  const user = {
    _id: "06c33e8b-e835-4736-80f4-63f44b66666c",
    name: "<PERSON>",
  };
  const dispatcher = {
    _id: "06c33e8b-e835-4736-80f4-63f44b10020a",
    name: "Dispatcher",
  };

  useEffect(() => {
    if (!liveMessage) {
      (async () => {
        const res = await MessagingService.getLoad(load.id);
        setData(res.data);
        const items = mapToText(res.data?.messages ?? []);
        setMessages(items);
      })();
    } else {
      const items = combineLists(
        messages,
        mapToText(liveMessage?.messages || [])
      );
      setMessages(items);
      setData({
        ...data!,
        ...liveMessage,
      });
    }
    return () => {
      setData(undefined);
      setMessages([]);
    };
  }, [liveMessage]);

  const mapToText = (messages: MessageItem[]) => {
    return messages
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .map(
        (x) =>
          ({
            user: x.from === "Driver" ? user : dispatcher,
            createdAt: new Date(x.date).getTime(),
            _id: generateUUID(),
            text: x.message,
            sent: true,
          } as IMessage)
      );
  };

  const mapToMessage = (messages: IMessage[]) => {
    return messages.map(
      (x) =>
        ({
          message: x.text,
          from: x.user._id === user._id ? "Driver" : "Dispatcher",
          to: x.user._id !== user._id ? "Driver" : "Dispatcher",
          date: new Date(x.createdAt).toISOString(),
          isRead: true,
        } as MessageItem)
    );
  };

  const combineLists = (list1: IMessage[], list2: IMessage[]) => {
    const seen = new Set();
    return [...list1, ...list2]
      .filter((x) => !seen.has(x.createdAt) && seen.add(x.createdAt))
      .sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
  };

  const onSend = async (
    texts: IMessage[] = [],
    items: IMessage[],
    data: Messaging
  ) => {
    try {
      const newText = {
        message: texts.at(0)?.text || "Empty",
        from: "Driver",
        to: "Dispatcher",
        date: new Date().toISOString(),
        isRead: false,
      } as MessageItem;
      const dataItems = [...mapToMessage(items), newText];

      const payload = {
        ...data,
        messages: dataItems,
      };
      Sentry.captureMessage(
        "--- before save Messaging ::: " + texts.at(0)?.text?.slice(0, 8)
      );
      await MessagingService.add(payload, load);
    } catch (error) {
      Sentry.captureException(error, {
        extra: {
          loadId: load.id,
          proId: load.shipment.proId,
          message: "Messaging (Chat) Error",
        },
      });
    }
  };

  const height = Dimensions.get("window").height;

  return (
    <Portal>
      <Dialog visible={show} dismissable={true} onDismiss={() => close()}>
        <Dialog.Title>
          <View
            style={{
              flex: 1,
              width: "100%",
              justifyContent: "space-between",
              flexDirection: "row",
              alignItems: "center",
            }}
          >
            <Text style={{ fontWeight: "bold", fontSize: 20 }}>Messaging</Text>
            <Pressable onPress={() => close()}>
              {() => (
                <Icon source="close" color={MD3Colors.error50} size={30} />
              )}
            </Pressable>
          </View>
        </Dialog.Title>
        <Dialog.Content>
          <View
            style={{
              height: height - 0.25 * height,
              borderWidth: 1,
              borderColor: MD3Colors.neutral100,
              borderRadius: 10,
              backgroundColor: "white",
            }}
          >
            <GiftedChat
              messages={messages}
              onSend={(texts) => onSend(texts, messages, data!)}
              user={user}
            />
          </View>
        </Dialog.Content>
      </Dialog>
    </Portal>
  );
}
