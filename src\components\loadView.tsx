import React, { useState } from "react";
import { Linking, View } from "react-native";
import { Button, Text } from "react-native-paper";
import FontAwesome5 from "@expo/vector-icons/FontAwesome5";
import { MaterialIcons } from "@expo/vector-icons";
import { LoadStatusType, MarketLoad } from "@core/models/load-market.model";
import { oweColors } from "@utils/Colors";
import { AddInfo, BolInfo } from "@core/models/bolInfo.model";
import AdditionForm from "./additional-form";
import StatusView from "./status-view";

const LoadView = ({
  load,
  submitForm,
  onAction,
}: {
  load: MarketLoad;
  submitForm: (data: AddInfo) => void;
  onAction: (data: BolInfo) => void;
}) => {
  const [open, setOpen] = useState(false);

  const shipment = load.shipment;
  const endpoint = load.shipment.routes[0];

  const isDropOffFlow =
    load.status === LoadStatusType.IN_TRANSIT &&
    load.shipment.routes[0].shipper.status === "ACCEPTED";

  const underReview = load.status === LoadStatusType.REVIEW;

  const onSubmit = (data: AddInfo) => {
    submitForm(data);
    setOpen(false);
  };

  const goToMap = () => {
    const address = isDropOffFlow
      ? endpoint.receiver.address.fullAddress
      : endpoint.shipper.address.fullAddress;
    Linking.openURL(
      "https://www.google.com/maps/search/?api=1&query=" + address
    );
  };

  return (
    <View style={{ padding: 16, display: "flex", gap: 40 }}>
      <StatusView load={load} click={(status) => onAction(status)} />

      {!underReview && (
        <View style={{ flexGrow: 1, alignContent: "center", padding: 4 }}>
          <View
            style={{
              borderBottomWidth: 1,
              borderColor: oweColors.tertiary.gray,
            }}
          >
            <Text style={{ color: oweColors.tertiary.gray, fontSize: 17 }}>
              {isDropOffFlow && (
                <MaterialIcons name="emoji-transportation" size={20} />
              )}
              {!isDropOffFlow && (
                <FontAwesome5 name="truck-loading" size={16} color="gray" />
              )}
              {isDropOffFlow ? " Receiver" : " Shipper"}
            </Text>
            <Text
              style={{ fontWeight: "700", marginVertical: 3, fontSize: 22 }}
            >
              {isDropOffFlow ? endpoint.receiver.name : endpoint.shipper.name}
            </Text>
          </View>
          <Button mode="text" onPress={() => goToMap()}>
            {isDropOffFlow
              ? endpoint.receiver.address.fullAddress
              : endpoint.shipper.address.fullAddress}
          </Button>
          <Text style={{ marginTop: 4, fontSize: 17 }}>
            Shipment ProID: {shipment.proId}
          </Text>
        </View>
      )}

      {underReview && (
        <View
          style={{
            flexGrow: 1,
            alignContent: "center",
            justifyContent: "center",
            gap: 20,
          }}
        >
          <Text style={{ fontWeight: "700", marginVertical: 3, fontSize: 20 }}>
            Additional Documents (Optional)
          </Text>
          <Button
            mode="contained"
            style={{ width: "40%", alignSelf: "center" }}
            onPress={() => setOpen(true)}
          >
            Upload
          </Button>
          <AdditionForm
            show={open}
            submit={onSubmit}
            close={() => setOpen(false)}
          />
        </View>
      )}
    </View>
  );
};

export default LoadView;
