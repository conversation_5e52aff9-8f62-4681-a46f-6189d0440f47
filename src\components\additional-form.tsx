import { useEffect, useRef, useState } from "react";
import * as ImagePicker from "expo-image-picker";
import { Pressable, ScrollView, View } from "react-native";
import { FontAwesome } from "@expo/vector-icons";
import { AddInfo } from "@core/models/bolInfo.model";
import { useCamera } from "./useCamera";
import { Button, Dialog, Portal, Text, TextInput } from "react-native-paper";
import ImageListView from "./image-view";

export default function AdditionForm({
  show,
  submit,
  close,
}: {
  show: boolean;
  close: () => void;
  submit: (data: AddInfo) => void;
}) {
  const [viewImage, setViewImage] = useState(false);
  const [form, setForm] = useState<AddInfo>({
    notes: "",
    pictures: [],
  });
  const [pictures, setPictures] = useState<ImagePicker.ImagePickerAsset[]>([]);
  const [error, setError] = useState({
    notesError: false,
    picturesError: false,
  });
  const { takePhoto } = useCamera();
  const [permission, requestPermission] = ImagePicker.useCameraPermissions();

  useEffect(() => {
    if (permission?.status !== ImagePicker.PermissionStatus.GRANTED) {
      requestPermission().then(() => {
        return null;
      });
    }
    return () => {
      setForm({
        notes: "",
        pictures: [],
      });
      setError({
        notesError: false,
        picturesError: false,
      });
    };
  }, []);

  const onTakePhoto = async () => {
    const res = await takePhoto();
    const images = [...pictures, ...res];
    setPictures(images);
    setError({
      ...error,
      picturesError: images.length <= 0 || images.length > 2,
    });
  };

  const onSubmit = () => {
    if (!form.notes || error.notesError) {
      setError({ ...error, notesError: true });
      return;
    }

    if (!pictures.length) {
      setError({ ...error, picturesError: true });
      return;
    }

    submit({
      notes: form.notes,
      pictures,
    });
  };

  const link = () => {
    return <Text>View({pictures.length})</Text>;
  };

  const updatePictures = (pictures: ImagePicker.ImagePickerAsset[]) => {
    setError({ ...error, picturesError: false });
    setPictures(pictures);
    setViewImage(false);
  };

  const onClose = () => {
    setError({ ...error, notesError: false, picturesError: false });
    setForm({ notes: "", pictures: [] });
    close();
  };

  return (
    <Portal>
      <Dialog visible={show} onDismiss={() => onClose()}>
        <Dialog.Title>
          <Text>Additional Details</Text>
        </Dialog.Title>
        <Dialog.Content>
          <View>
            <Text style={{ fontWeight: "700" }}>Take Proof Picture</Text>
            <View
              style={{
                width: "100%",
                display: "flex",
                flexDirection: "row",
                gap: 4,
                justifyContent: "space-between",
                marginVertical: 16,
              }}
            >
              <Pressable onPress={onTakePhoto}>
                {({ pressed }) => (
                  <FontAwesome
                    name="camera"
                    size={25}
                    style={{ marginRight: 15, opacity: pressed ? 0.5 : 1 }}
                  />
                )}
              </Pressable>

              <Pressable onPress={() => setViewImage(true)}>
                {() => (pictures.length ? link() : null)}
              </Pressable>
            </View>
            {error.picturesError && (
              <Text style={{ color: "red", marginBottom: 16 }}>
                Requires 1 or 2 pictures only.
              </Text>
            )}
          </View>
          {viewImage && pictures.length && (
            <ImageListView
              show={viewImage}
              files={pictures}
              close={updatePictures}
            />
          )}
          <View>
            <TextInput
              label="Notes"
              mode="outlined"
              returnKeyType="done"
              error={error.notesError}
              onChangeText={(text) => {
                const value = text;
                const isValid = value.length > 2;
                setError({ ...error, notesError: !isValid });
                if (isValid) {
                  setForm({ ...form, notes: value });
                }
              }}
            />
            {error.notesError && (
              <Text style={{ color: "red" }}>Please enter comments.</Text>
            )}
          </View>
        </Dialog.Content>
        <Dialog.Actions>
          <Button onPress={() => onClose()}>Cancel</Button>
          <Button
            mode="contained"
            style={{ paddingHorizontal: 16 }}
            onPress={() => onSubmit()}
          >
            Submit
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );
}
