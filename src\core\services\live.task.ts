import * as TaskManager from "expo-task-manager";
import * as Location from "expo-location";
import { combineLists, getFromCache, tryPromise } from "@utils/app.settings";
import { TrackingService } from "./tracking.service";
import { StorageKeys } from "../models/common";
import * as Sentry from "@sentry/react-native";
import { MarketLoad, LoadStatusType } from "../models/load-market.model";
import { Tracking, TrackPoint } from "../models/tracking.model";

const LOCATION_TASK_NAME = "background-location-task";

export const isShipmentComplete = (status: LoadStatusType): boolean => {
  const completedStatuses = [
    LoadStatusType.DELIVERED,
    LoadStatusType.REVIEW,
    LoadStatusType.CANCELLED,
  ];
  return completedStatuses.includes(status);
};

export const enableLiveLocationTracking = async (data: MarketLoad) => {
  try {
    if (!data) {
      console.log(
        "----- background-location-task operation skipped shipment not found -------"
      );
      return;
    }

    const interval = 1000 * 60 * 5;

    const { status: foregroundStatus } =
      await Location.requestForegroundPermissionsAsync();
    if (foregroundStatus === "granted") {
      const { status: backgroundStatus } =
        await Location.requestBackgroundPermissionsAsync();

      if (backgroundStatus === "granted") {
        await Location.startLocationUpdatesAsync(LOCATION_TASK_NAME, {
          accuracy: Location.Accuracy.BestForNavigation,
          deferredUpdatesInterval: interval,
          distanceInterval: 30,
          foregroundService: {
            notificationTitle: "1-Way Express Driver App ",
            notificationBody: "Location is used when App is in background",
          },
          activityType: Location.ActivityType.AutomotiveNavigation,
          showsBackgroundLocationIndicator: true,
        });
      } else {
        Sentry.captureMessage("--backgroundStatus-- Denied");
      }
    } else {
      Sentry.captureMessage("--foregroundStatus-- Denied");
    }
  } catch (error) {
    console.log(
      "----- background-location-task operation failed with error: ------",
      error
    );
    Sentry.captureException(error);
  }
};

TaskManager.defineTask(LOCATION_TASK_NAME, async ({ data, error }: any) => {
  try {
    const tracking = await getFromCache<Tracking>(StorageKeys.TRACKING);
    const load = await getFromCache<MarketLoad>(StorageKeys.LOAD);
    const tracks = (await getFromCache<TrackPoint[]>(StorageKeys.TRACKS)) || [];

    if (!tracking) {
      console.log("----- background-location-task operation skipped ------");
      Sentry.captureMessage(
        "----- background-location-task o:: could not find tracking obj ------"
      );
      return;
    }

    if (!load) {
      console.log(
        "----- background-location-task operation skipped - no load found ------"
      );
      Sentry.captureMessage(
        "----- background-location-task o:: could not find load obj ------"
      );
      return;
    }

    // Check if shipment is complete - stop tracking for completed shipments
    if (isShipmentComplete(load.status)) {
      console.log(
        `----- background-location-task operation skipped - shipment is ${load.status} ------`
      );
      // Stop location tracking for completed shipments
      await Location.stopLocationUpdatesAsync(LOCATION_TASK_NAME);
      return;
    }

    if (error) {
      console.log(
        "background-location-task operation failed with error: ",
        error
      );
      Sentry.captureException(error);
      return;
    }
    if (data) {
      const location = {
        lat: data.locations.at(0)?.coords.latitude,
        lng: data.locations.at(0)?.coords.longitude,
      };
      const pins = combineLists(
        [
          ...(tracking?.driverTracks ?? []),
          {
            latitude: location.lat,
            longitude: location.lng,
            date: new Date().toISOString(),
          } as TrackPoint,
        ],
        tracks
      );

      const latestItem = pins.reduce((latest, current) => {
        return new Date(current.date) > new Date(latest.date)
          ? current
          : latest;
      }, pins[0]);
      const payload = {
        ...tracking,
        latestLocation: {
          lat: latestItem.latitude,
          lng: latestItem.longitude,
        },
        driverTracks: pins,
      };
      TrackingService.updateTracksCache(pins);
      const [error, _] = await tryPromise(
        TrackingService.updateTracking(payload, load?.driver?.name!)
      );
      if (error) {
        Sentry.captureException(error);
      }
    }
  } catch (error) {
    Sentry.captureException(error);
  }
});

export const disableLiveLocationTracking = async () => {
  try {
    console.log("----- Stopping background location tracking ------");
    await Location.stopLocationUpdatesAsync(LOCATION_TASK_NAME);
    console.log(
      "----- Background location tracking stopped successfully ------"
    );
  } catch (error) {
    console.log(
      "----- Failed to stop background location tracking ------",
      error
    );
    Sentry.captureException(error);
  }
};
