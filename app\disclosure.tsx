import React, { useEffect, useState } from "react";
import { View, Image, SafeAreaView, Platform } from "react-native";
import {
  Button,
  TextInput,
  Card,
  Text,
  ActivityIndicator,
  MD2Colors,
} from "react-native-paper";
import * as Location from "expo-location";
import { oweColors } from "@utils/Colors";
import * as Updates from "expo-updates";
import * as Notifications from "expo-notifications";
import { Strategy } from "@core/models/common";
import { useRouter } from "expo-router";
import { useStrategy } from "@/src/context/main.context";

const DisclosureScreen = () => {
  const [status, requestPermission] = Location.useForegroundPermissions();
  const [loading, setLoading] = useState(false);
  const [denied, setDenied] = useState<boolean | null>(null);
  const { data, setStrategy } = useStrategy();
  const router = useRouter();

  useEffect(() => {
    (async () => {
      try {
        const update = await Updates.checkForUpdateAsync();
        if (update.isAvailable) {
          await Updates.fetchUpdateAsync();
          Updates.reloadAsync();
        }
      } catch (e) {}
    })();
  }, []);

  const requestLocationPermission = async (accepted: boolean) => {
    try {
      setStrategy({
        ...data,
        locationGranted: status?.status === Location.PermissionStatus.GRANTED,
      });
      if (status?.status !== Location.PermissionStatus.GRANTED) {
        let res = await requestPermission();
        if (res.status !== Location.PermissionStatus.GRANTED) {
          res = await requestPermission();
        }
        console.log("Location response ::: ", res);
        setDenied(res.status !== Location.PermissionStatus.GRANTED);
        setStrategy({
          ...data,
          locationGranted: res.status === Location.PermissionStatus.GRANTED,
        });
      }
      console.log(status?.status);
      if (data.type === Strategy.DONE) {
        router.push("/complete");
      }
      if (data.type === Strategy.LIVE) {
        router.push("/home");
      }
      if (data.type === Strategy.SIGIN) {
        router.push("/signIn");
      }
    } catch (error) {
      setStrategy({ ...data, locationGranted: false });
    }
  };

  return (
    <SafeAreaView
      style={{
        display: "flex",
        height: "100%",
      }}
    >
      <View
        style={{
          flex: 1,
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          width: "100%",
          paddingHorizontal: 36,
        }}
      >
        <Card style={{ width: "100%", paddingVertical: 35 }}>
          <Card.Content>
            <View style={{ height: 150 }}>
              <Image
                style={{
                  width: "100%",
                  resizeMode: "contain",
                  height: 80,
                }}
                source={require("../assets/images/logo-long.png")}
              />
              <Text
                style={{
                  alignSelf: "center",
                  color: oweColors.primary[100],
                  fontSize: 24,
                  marginTop: 20,
                  fontWeight: "bold",
                }}
              >
                Location Permission
              </Text>
            </View>
            {!loading && (
              <View>
                <Text
                  style={{
                    alignSelf: "center",
                    color: oweColors.primary[100],
                    fontSize: 16,
                    marginTop: 2,
                    fontWeight: "500",
                    textAlign: "justify",
                  }}
                >
                  {denied
                    ? "To use this Application, please enable location permission in your device settings and reload the app."
                    : `The 1 Way Express Driver app collects location data to enable Driver and shipment tracking even when the app is closed or not in use. This information is only used to enable shipment tracking purposes. To allow tracking, click continue.`}
                </Text>
              </View>
            )}
            {loading && (
              <ActivityIndicator
                animating={true}
                size={"large"}
                color={MD2Colors.orange700}
              />
            )}
          </Card.Content>
          {!denied && (
            <Card.Actions style={{ marginTop: 20 }}>
              {Platform.OS !== "ios" && <Button onPress={() => setDenied(true)}>Cancel</Button>}
              <Button onPress={() => requestLocationPermission(true)}>
                Continue
              </Button>
            </Card.Actions>
          )}
        </Card>
      </View>
    </SafeAreaView>
  );
};

export default DisclosureScreen;
