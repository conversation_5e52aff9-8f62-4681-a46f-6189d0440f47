/**
 * Map that shows list of stops and locations/pings for a single load
 */
// eslint-disable-next-line no-unused-vars
class MacroPointLoadMap {
    // --- const & 'readonly' variables initialized during constructor ---

    /**
       * HTML div id that should contain the map this class is mananging.  All created objects and elemements in this class are suffixed with this id
       */
    #ID_SELECTOR;

    // route/stops specific map objects
    #ROUTE_LAYER;
    #ROUTE_LAYER_ID;
    #ROUTE_SOURCE_ID;
    #ROUTE_SOURCE;
    #ROUTE_MARKER_LAYER_ID;
    #STOP_LAYER_ID;
    #STOP_SOURCE_ID;


    // ping/location specific map objects
    #PING_SOURCE_ID;
    #PING_LAYER_ID;

    // single instance of a custom marker for last location/ping
    #PING_LASTPING_MARKER;
    #PING_LASTPING_DIV_ID;


    /**
       * Configuration for this map passed into constructor (readonly)
       */
    #MAP_OPTIONS;

    // --- (end const) ---

    // --- private class fields ---

    /**
     * contains the displaying TrimbleMaps.Popup
     */
    #activePopup;

    /**
       * Reference to TrimbleMap that is managed by this class
       */
    #trimbleMap;

    /**
       * Current ping locations. Used for change detection (GeoJson object)
       */
    #currentPingFeatures;

    /**
       * Current route locations. Used for change detection (GeoJson object)
       */
    #currentRouteFeatures;

    /**
     * Current stop locations.  Used for change detection (GeoJson object)
     */
    #currentStopFeatures;

    /**
 * Observer that listens for size change events on the map container div
 */
    #mapChangeObserver;

    // -- (end private class fields) --

    /**
       * Constructor
       * @param {string} divId HTML div id that should contain the main this class is mananging.  All created objects and elemements in this class are suffixed with this id
       * @param {*} locationData Object containing 'pings','routes', and 'stops' fields as GeoJson (FeatureList(Point)) objects
       * @param {*} uiSettings Colors to use when drawing map, markers, routes, and text
       */
    constructor(mapOptions, locationData) {
        if (typeof (mapOptions.showTraffic) === 'undefined') {
            mapOptions.showTraffic = false;
        }
        if (typeof (mapOptions.showWeather) === 'undefined') {
            mapOptions.showTraffic = false;
        }

        this.#MAP_OPTIONS = mapOptions;

        this.#ID_SELECTOR = this.#MAP_OPTIONS.divHostId;

        this.#STOP_LAYER_ID = `stop_layer_${this.#ID_SELECTOR}`;
        this.#STOP_SOURCE_ID = `stop_source_${this.#ID_SELECTOR}`;

        this.#ROUTE_LAYER_ID = `route_layer_${this.#ID_SELECTOR}`;
        this.#ROUTE_SOURCE_ID = `route_source${this.#ID_SELECTOR}`;
        this.#ROUTE_MARKER_LAYER_ID = `route_maker_layer${this.#ID_SELECTOR}`;

        this.#PING_SOURCE_ID = `ping_source_${this.#ID_SELECTOR}}`;
        this.#PING_LAYER_ID = `ping_marker_layer_${this.#ID_SELECTOR}`;
        this.#PING_LASTPING_DIV_ID = `ping_marker_${this.#ID_SELECTOR}`;

        const initialMapBounds = this.#findBounds(locationData.bounds);

        var mapStyleString = (mapOptions.mapStyle == null || mapOptions.mapStyle == '')
            ? this.#MAP_OPTIONS.mapStyle
            : mapOptions.mapStyle;

        // eslint-disable-next-line no-undef
        this.#trimbleMap = new TrimbleMaps.Map({
            container: this.#ID_SELECTOR,
            style: mapStyleString ?? 'transportation',
            center: mapOptions?.centerCoordinates,
            bounds: initialMapBounds,
            trackResize: true,
            touchPitch: false,
            dragRotate: false,
            minZoom: 1.25
        });
        this.#mapChangeObserver = new ResizeObserver(entries => {
            if (this.#trimbleMap) {
                this.#trimbleMap.resize();
            }
        });

        var nav = new TrimbleMaps.NavigationControl({
            showCompass: false,

        });
        this.#trimbleMap.addControl(nav, 'top-left');

        const thisProxy = this; // capture 'this' so we can reuse it in promise scoped code
        this.#trimbleMap.on('error', function (e) {
            console.error(e);
            console.dir(e);
        })
        this.#trimbleMap.on('load', function () {
            const loadProxy = thisProxy;
            const map = thisProxy.#trimbleMap;
            const filePath = '/maps/images/stop.png';

            map.loadImage(filePath, function (error, image, map) { // load image then continue building routing layer MacroPointMapConst.ROUTE_STOP_IMAGE_URL
                if (error) {
                    console.error(`Error reading image from ${filePath} ${error}`);
                    throw error;
                    return;
                }
                thisProxy.#trimbleMap.addImage('stop', image);
                loadProxy.#initializeMapStops(locationData.stops, loadProxy)
                thisProxy.setData(locationData, loadProxy);
                thisProxy.setWeather(thisProxy.#MAP_OPTIONS.showWeather, loadProxy);
                thisProxy.setTraffic(thisProxy.#MAP_OPTIONS.showTraffic, loadProxy);
                // start listening for size change events on the map's container element
                if (loadProxy.#mapChangeObserver) {
                    const mapHtmlContainer = document.getElementById(loadProxy.#ID_SELECTOR);
                    if (mapHtmlContainer) {
                        loadProxy.#mapChangeObserver.observe(mapHtmlContainer);
                    }                    
                }
               
            });
        });
    }

    // --- public 'interface' methods ---
    /**
       * Explictly set a style for the map.  Values must be supported a supported style for TrimbleMaps or results are Trimble defined
       * @param {string} styleId one of the well known Trimble styles to set map to
       */
    setStyle(styleId) {
        this.#trimbleMap.setStyle(styleId);
    }

    /**
       * Toggle traffic overlay on/off.
       */
    toggleTraffic() {
        if (this.#trimbleMap) {
            this.#trimbleMap.toggleTrafficVisibility();
        }
    }

    /**
      *
      * @param {boolean} newDisplayState True to turn on traffic layer
        * @param {*} proxy reference to base class instance since this might be executing inside a promise context
      */
    setTraffic(newDisplayState, proxy) {
        proxy = proxy ?? this;
        if (typeof (newDisplayState) === 'undefined') {
            newDisplayState = false;
        }
        const currentDisplayState = proxy.#trimbleMap.isTrafficVisible;
        if (currentDisplayState != newDisplayState) {
            proxy.#trimbleMap.setTrafficVisibility(newDisplayState);
        }
    }


    /**
       * Toggle weather radar on/off
       */
    toggleWeather() {
        if (this.#trimbleMap) {
            this.#trimbleMap.toggleWeatherRadarVisibility();
        }
    }

    /**
       *
       * @param {boolean} newDisplayState True to turn on weather layer, undefined/false to turn off weather
       * @param {*} proxy reference to base class instance since this might be executing inside a promise context      
       */
    setWeather(newDisplayState, proxy) {
        proxy = proxy ?? this;
        if (typeof (newDisplayState) === 'undefined') {
            newDisplayState = false;
        }
        const currentDisplayState = proxy.#trimbleMap.isWeatherVisible;
        if (currentDisplayState != newDisplayState) {
            proxy.#trimbleMap.setWeatherRadarVisibility(newDisplayState);
        }
    }

    /**
       * Set/update pings and stops
       *
       * @param {*} locationData 'pings','route', and 'stops' attribute containing GeoJson (FeatureList(Point)) objects
       * @param {*} proxy reference to base class instance since this might be executing inside a promise context
       */
    setData(locationData, proxy) {
        proxy = proxy ?? this;

        proxy.#setPingData(locationData?.pings, proxy);
        proxy.#setStopData(locationData?.stops, proxy);

    }

    /**
       * Remove the managed map, webworkers, and HTML objects associated with this map.  Important class fields are set to 'undefined'  After this call, the class is
       * in an indeterminate state and shouldn't be used again.
       */
    remove() {
        if (this.#mapChangeObserver) {
            this.#mapChangeObserver.disconnect();
            this.#mapChangeObserver = undefined;
        }

        if (this.#ROUTE_LAYER) {
            this.#ROUTE_LAYER;
        }

        if (!this.#PING_LASTPING_MARKER) {
            this.#PING_LASTPING_MARKER = undefined;
        }
        if (this.#trimbleMap) {
            this.#trimbleMap.remove();
            this.#trimbleMap = undefined;
        }

        this.#currentPingFeatures = undefined;
        this.#currentRouteFeatures = undefined;
        this.currentStopFeatures = undefined;

    }
    // --- (end public 'interface' methods) ---


    #initializeMapStops(geoJsonStops, proxy) {
        proxy = proxy ?? this;

        const ids = {
            routeLayerId: proxy.#ROUTE_LAYER_ID,
            routeSourceId: proxy.#ROUTE_SOURCE_ID,
            markerLayerId: proxy.#ROUTE_MARKER_LAYER_ID
        }
        // create a specialized routing layer
        try {
            const map = proxy.#trimbleMap;
            proxy.#ROUTE_LAYER = new TrimbleMaps.Route({
                routeId: ids.routeLayerId,
                stops: MacroPointLoadMap.#geoJsonFeaturesToCoordinateArray(geoJsonStops),
                routeColor: proxy.#MAP_OPTIONS.colors.routeColor,
                showStops: false,
                frameOptions: {
                    animate: false,
                    padding: {
                        top: 50,
                        bottom: 50,
                        left: 50,
                        right: 50,
                    },
                },
            });

            proxy.#ROUTE_LAYER.addTo(map);
            var isRouteHidden = typeof (proxy.#MAP_OPTIONS.isRouteHidden) != 'undefined' && proxy.#MAP_OPTIONS.isRouteHidden;
            if (isRouteHidden) {
                proxy.#ROUTE_LAYER.update({
                    // If Route is hidden, make it invisible.
                    // myRoute.setVisibility(false); is not working, don't even try
                    routeColor: 'rgba(255, 255, 255, 0.0)'
                });
            }
            

            map.addSource(ids.routeSourceId, geoJsonStops);
            proxy.#ROUTE_SOURCE = map.getSource(ids.routeSourceId);

            // add stop icon layer (octagon) and stop number ignoring first stop
            map.addLayer({
                id: ids.markerLayerId,
                source: ids.routeSourceId,
                type: 'symbol',
                filter: ['>', ['get', 'label'], ''], // do NOT number first stop (this probably should be configurable)
                layout: {
                    'icon-image': 'stop',
                    'icon-size': 0.0625, // 512px*0.625 = 32px;
                    'text-field': '{label}',
                    'text-justify': 'center',
                    'icon-allow-overlap': true,
                    'text-allow-overlap': true,
                },
                paint: {
                    'text-color': proxy.#MAP_OPTIONS.colors.stopTextColor,
                },
            });
        }
        catch (e) {
            console.log(e);
        }
    }

    /**
       *
       * @param {GeoJson} geoJsonPings Full GeoJson (FeatureList(Point)) object list of pings
        * @param {*} proxy reference to base class instance since this might be executing inside a promise context 
        * @returns
       */
    #setPingData(geoJsonPings, proxy) {
        proxy = proxy ?? this;
        const ids = {
            sourceId: proxy.#PING_SOURCE_ID,
            layerId: proxy.#PING_LAYER_ID
        };
        // last entry in the feature list MUST have 'lastPing:true' property so the UI knows not to display a marker
        // for the last feature
        if (geoJsonPings?.data?.features?.length) {
            const lastFeature = geoJsonPings.data.features[geoJsonPings.data.features.length - 1];
            if (lastFeature?.properties) {
                lastFeature.properties.lastPing = true;
            } else {
                lastFeature.properties = {
                    lastPing: true,
                };
            }
        }

        const map = proxy.#trimbleMap;
        const pingSource = map.getSource(ids.sourceId);
        const pingLayer = map.getLayer(ids.layerId);

        if (geoJsonPings) {
            if (!pingSource) {
                map.addSource(ids.sourceId, geoJsonPings);
            }
            else {
                pingSource.setData(geoJsonPings.data);
            }
            if (!pingLayer) {
                //map.addLayer({
                //    id: ids.layerId,
                //    source: ids.sourceId,
                //    type: 'circle',
                //    filter: ['!', ['has', 'lastPing']], // display all locations/pings except the last one
                //    paint: {
                //        'circle-color': proxy.#MAP_OPTIONS.colors.locationFillColor,
                //        'circle-radius': 8,
                //        'circle-stroke-width': MacroPointMapConst.NONCLUSTERED_MARKER_BORDER_STROKE,
                //        'circle-stroke-color': proxy.#MAP_OPTIONS.colors.locationBorderColor,
                //    },
                //});
                
                map.addLayer({
                    id: ids.layerId,
                    source: ids.sourceId,
                    type: 'symbol',
                    filter: ['!', ['has', 'lastPing']], // display all locations/pings except the last one
                    layout: {
                        'icon-image': 'circle-fill-blue',
                        'icon-allow-overlap': true,
                        'text-allow-overlap': true,
                        'text-offset': [0, 0],
                        'text-anchor': 'center',
                        'text-font': ['Roboto Regular'],
                        'text-optional': true,
                        'text-field': [
                            'format',
                            ['get', 'ordinal'], { 'font-scale': 0.7, 'text-color': 'white' }
                        ]
                    }
                });
                map.on('click', ids.layerId, (e) => {
                    if (typeof (e.features) != 'undefined' && e.features.length > 0) {
                        const coordinates = e.features[0].geometry.coordinates.slice();
                        const popup = this.#newPopup();
                        popup.setLngLat(coordinates).setHTML(this.#getLocationPopupHtml(e.features)).addTo(map);
                    }
                });
                map.on('mouseenter', ids.layerId, () => {
                    map.getCanvas().style.cursor = 'pointer';
                });
                map.on('mouseleave', ids.layerId, () => {
                    map.getCanvas().style.cursor = '';
                });
                
            }
        }
        else {
            if (pingLayer) {
                map.removeLayer(ids.layerId);
            }
            if (pingSource) {
                map.removeSource(ids.sourceId);
            }
        }

        // add last marker icon
        if (geoJsonPings?.data?.features?.length) {
            this.#setLastPing(map, geoJsonPings?.data?.features[geoJsonPings.data.features.length - 1], proxy);
        } else {
            this.#setLastPing(map, undefined, proxy); // remove marker as there are not markers in the data
        }
    }

#newPopup() {
    if (this.#activePopup) {
        this.#activePopup.remove();
        this.#activePopup = undefined;
    }
    if (!this.#activePopup) {
        this.#activePopup = new TrimbleMaps.Popup({
            closeButton: false,
            closeOnClick: true,
            closeOnMove: true,
            maxWidth: 'none',
        });
    }
    return this.#activePopup;
}

    #getFeatureAsMarkup(feature, isLastPing, lastPingBg) {
        var lasPingHeader = '';
        if (isLastPing) {
            lasPingHeader = `<div class="pl-2" style="background-color:${lastPingBg}; color: white; margin-left: -8px; margin-right: -8px;">
                Last ping
            </div>`;
        }
        return `<div>${lasPingHeader}
            <div class="map-carrier-name">${feature.properties.ordinal}. ${feature.properties.pingLocation} </div>
            <div class="map-status-container">
                <div class="map-status-values small">
                   <span><i class="fa-duotone fa-map-marker"></i> ${feature.properties.coordinatesDisplay} </span>
                   <span class="ml-2"><i class="fa-duotone fa-calendar"></i> ${feature.properties.pingDate} ${feature.properties.pingTimezone}</span>
                </div>
            </div>
        </div>
    `;
    }

    #getLocationPopupHtml(features) {
        var result = '';
        features.forEach(feature => {
            result += this.#getFeatureAsMarkup(feature);
        });

        return result;
    }

    //#getLocationPopupHtml(features) {
    //    var result = 
    //        `<div>
    //        <div class="map-carrier-name">${features[0].properties.ordinal}. ${features[0].properties.pingLocation} </div>
    //        <div class="map-status-container">
    //            <div class="map-status-values small">
    //               <span><i class="fa-duotone fa-map-marker"></i> ${features[0].properties.coordinatesDisplay} </span>
    //               <span class="ml-2"><i class="fa-duotone fa-calendar"></i> ${features[0].properties.pingDate} ${features[0].properties.pingTimezone}</span>
    //            </div>
    //        </div>
    //    </div>    
    //    <div>Please zoom-in to see more 147 locations</div>
    //`;
        

    //    return result;
    //}

    /**
   * Update the data used for displaying 'stops' on the map
   *
   * @param {*} geoJsonFeatures Full GeoJson (FeatureList(Point)) object list of stops
   * @param {*} proxy reference to base class instance since this might be executing inside a promise context
   * @returns
   */
    #setStopData(geoJsonFeatures, proxy) {
        proxy = proxy ?? this;


        // update route layer with new set of route coordinates
        const routeId = proxy.#ROUTE_LAYER.getRouteId();
        this.#ROUTE_LAYER.update({
            routeId,
            stops: MacroPointLoadMap.#geoJsonFeaturesToCoordinateArray(geoJsonFeatures),
        });

        // update source that displays the text
        proxy.#ROUTE_SOURCE.setData(geoJsonFeatures.data);
    }

    // https://developer.trimblemaps.com/maps-sdk/api/#lnglatboundslike
    #findBounds(geoJsonFeatureBounds) {
        if (!geoJsonFeatureBounds?.bbox) return undefined;
        const retVal = geoJsonFeatureBounds.bbox;
        return retVal;
    }

    /**
       * Converts GeoJson(FeatureList(point)) object into a array(array) of lng/lat suitable for routing layer
       * @param {*} geoJsonFeatureListObject GeoJson feature list object
       * @returns Array of [lng,lat]
       */
    static #geoJsonFeaturesToCoordinateArray(geoJsonFeatureListObject) {
        const retVal = [];
        if (geoJsonFeatureListObject?.data?.features) {
            geoJsonFeatureListObject.data.features.forEach((f) => {
                retVal.push([f.geometry.coordinates[0], f.geometry.coordinates[1]]);
            });
        }
        return retVal;
    }

    /**
        * Set/Move the last ping icon on the map
        *
        * @param {TrimbleMaps.Map} map Reference to map that will contain this icon
        * @param {GeoJsonFeature} feature Location to display last ping icon.  If null, marker will be removed.
        * @param {*} proxy reference to base class instance since this might be executing inside a promise context
         */
    #setLastPing(map, feature, proxy) {
        proxy = proxy ?? this;
        console.log('#setLastPing::Setting last ping');
        // feature is null so remove last marker if any
        if (!feature) {
            // clear any existing popup
            if (proxy.#PING_LASTPING_MARKER) {
                proxy.#PING_LASTPING_MARKER.setPopup();
                proxy.#PING_LASTPING_MARKER.remove();
                proxy.#PING_LASTPING_MARKER = undefined;
            }
            $('.trimblemaps-marker').off('mouseover');
            $('.trimblemaps-marker').off('mouseleave');
            return;
        }

        // feature is last ping 

        // we don't have a last ping marker yet so create one.
        if (!proxy.#PING_LASTPING_MARKER) {
            console.log('#setLastPing::setting up marker');
            // set pin marker
            const svgContent = document.createElement('div');
            svgContent.id = `${proxy.#PING_LASTPING_DIV_ID}`;

            svgContent.innerHTML = `
            <div id="my-marker">
            <span class="map-last-ping-marker fa-stack small" style="color:${proxy.#MAP_OPTIONS.colors.lastLocationColor ?? 'black'}">
                <i class="fa-solid fa-circle fa-stack-2x"></i>
                <i class="fa-solid fa-location-dot fa-stack-1x fa-inverse"></i>
            </span>
            </div>
           `;
            proxy.#PING_LASTPING_MARKER = new TrimbleMaps.Marker({
                element: svgContent,
            })
                .setLngLat(feature.geometry.coordinates)
                .addTo(map);
        }

        // set last ping marker's location
        console.log('#setLastPing::setting marker position');
        proxy.#PING_LASTPING_MARKER.setLngLat(feature.geometry.coordinates);

        // clear any existing popup
        let existingPopup = proxy.#PING_LASTPING_MARKER.getPopup();
        if (existingPopup) {
            console.log('#setLastPing::clearing existing popup');
            proxy.#PING_LASTPING_MARKER.setPopup(null);
            existingPopup.remove();
        }

        // remove event handlers associated with this
        $('.trimblemaps-marker').off('mouseover');
        $('.trimblemaps-marker').off('mouseleave');

        // create new popup
        console.log('#setLastPing::creating new popup on marker');
        proxy.#PING_LASTPING_MARKER.setPopup(new TrimbleMaps.Popup({
            closeButton: false,
            closeOnMove: true
        }));

        // attach event handlers
        $('.trimblemaps-marker').mouseover({ feature, proxy, map, marker: proxy.#PING_LASTPING_MARKER }, function (e) {
            console.log('#setLastPing::onMouseOver marker');
            var pop = e.data.marker.getPopup();
            pop.setHTML(proxy.#getPopupHtml(feature, proxy));
            if (!pop.isOpen()) {
                e.data.marker.togglePopup();
            }

        });
        $('.trimblemaps-marker').mouseleave({ feature, proxy, map, marker: proxy.#PING_LASTPING_MARKER }, function (e) {
            console.log('#setLastPing::onMouseLeave marker');
            var pop = e.data.marker.getPopup();
            if (pop.isOpen()) {
                e.data.marker.togglePopup();
            }
        });
    }
    
    #getPopupHtml(feature, proxy) {
        proxy = proxy ?? this;
        if (!feature?.properties?.shipmentUid) {
            // eslint-disable-next-line no-param-reassign
            feature.properties.shipmentUid = 'my uid';
        }
        //const params = feature.properties;
        //const message = {

        //    pingDate: params[proxy.#MAP_OPTIONS.paramKeys.pingDate],
        //    pingTimezone: params[proxy.#MAP_OPTIONS.paramKeys.pingTimezone],
        //    pingLocation: params[proxy.#MAP_OPTIONS.paramKeys.pingLocation],
        //    coordinatesTitle: params[proxy.#MAP_OPTIONS.paramKeys.coordinatesTitle],
        //    coordinatesDisplay: params[proxy.#MAP_OPTIONS.paramKeys.coordinatesDisplay]
        //};

        return this.#getFeatureAsMarkup(feature, true, proxy.#MAP_OPTIONS.colors.lastLocationColor ?? 'black');

    //    const retVal = `
    //    <div style="padding-left:0.1em">
    //    YoYo
    //        <div class="map-address">${message.pingLocation}</div>
    //        <div class="map-coordinates" style="opacity:0.7;">${message.coordinatesTitle} ${message.coordinatesDisplay}</div>
    //        <hr style="margin:0px;margin-bottom:0.25em;"/>
    //        <div class="map-date-container">
    //            <div>${message.pingDate}</div>
    //            <div class="map-date-timezone">${message.pingTimezone}</div> 
    //        </div>
            
    //    </div>
       
    //`;
    //    return retVal;
    }

}
