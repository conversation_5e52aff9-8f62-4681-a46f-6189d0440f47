import { MarketLoad } from "../models/load-market.model";
import { Messaging } from "../models/tracking.model";
import { getId, supabase } from "@utils/app.settings";
import * as Sentry from "@sentry/react-native";

export class MessagingService {
  static async getLoad(
    id: string
  ): Promise<{ data?: Messaging; error?: string }> {
    const { data, error } = await supabase
      .from("messagings")
      .select("*")
      .eq("load", id)
      .limit(1)
      .single<Messaging>();

    if (error) {
      Sentry.captureException(error);
      return { error: "unable to get load" };
    }
    return { data };
  }

  static async add(
    item: Messaging,
    load: MarketLoad
  ): Promise<{ data?: Messaging; error?: string }> {
    let { id, ...payload } = item;

    if (!id) {
      const res = await supabase
        .from("messagings")
        .select("id")
        .eq("load", load.id)
        .limit(1)
        .single<Messaging>();
      id = res.data?.id!;
    }

    const { error, data } = await supabase
      .from("messagings")
      .upsert(
        id
          ? {
              ...payload,
              id,
              load: getId(load),
              updatedName: load.driver.name,
            }
          : {
              ...payload,
              createdBy: load.createdBy,
              load: getId(load),
              accountId: getId(load.broker),
              createdName: load.driver.name,
            }
      )
      .select("*");
    return {
      data: (data ?? []).at(0) as Messaging,
      error: error?.message,
    };
  }
}
