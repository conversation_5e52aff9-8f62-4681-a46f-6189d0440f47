import React from "react";
import { Button, Text } from "react-native-paper";
import FontAwesome5 from "@expo/vector-icons/FontAwesome5";
import { MaterialIcons } from "@expo/vector-icons";
import { MarketLoad } from "@core/models/load-market.model";
import { Linking, View } from "react-native";
import { Shipment } from "@core/models/shipment.model";

export default function ShipmentView({ load }: { load: MarketLoad }) {
  const shipment = load.shipment;
  const endpoint = load.shipment.routes[0];

  return (
    <View style={{ padding: 16, backgroundColor: "white", borderRadius: 8 }}>
      <Text style={{ fontWeight: "700", fontSize: 22, textAlign: "center" }}>
        Shipment Details
      </Text>
      <View style={{ display: "flex", gap: 16, marginTop: 16 }}>
        <Text>Shipment Pro ID: {shipment.proId}</Text>
        <View
          style={{
            display: "flex",
            gap: 4,
          }}
        >
          <View>
            <Text style={{ color: "gray" }}>
              <FontAwesome5 name="truck-loading" size={16} color="gray" />{" "}
              Shipper
            </Text>
            <Text style={{ fontSize: 20, fontWeight: "700" }}>
              {endpoint.shipper.name}
            </Text>
          </View>
          <Button
            mode="text"
            onPress={() =>
              Linking.openURL(
                "https://www.google.com/maps/search/?api=1&query=" +
                  endpoint.shipper.address.fullAddress
              )
            }
          >
            {endpoint.shipper.address.fullAddress}
          </Button>
        </View>
        <View
          style={{
            display: "flex",
            gap: 4,
          }}
        >
          <View>
            <Text style={{ color: "gray" }}>
              <MaterialIcons name="emoji-transportation" size={20} /> Receiver
            </Text>
            <Text style={{ fontSize: 20, fontWeight: "700" }}>
              {endpoint.receiver.name}
            </Text>
          </View>
          <Button
            mode="text"
            onPress={() =>
              Linking.openURL(
                "https://www.google.com/maps/search/?api=1&query=" +
                  endpoint.receiver.address.fullAddress
              )
            }
          >
            {endpoint.receiver.address.fullAddress}
          </Button>
        </View>
        <View
          style={{
            display: "flex",
            gap: 4,
            borderTopWidth: 1,
            paddingTop: 10,
          }}
        >
          <View
            style={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-between",
              gap: 4,
              alignItems: "center",
            }}
          >
            <Text style={{ fontSize: 17 }}>Quantity/BOL Type:</Text>
            <Text style={{ fontSize: 17 }}>
              {shipment.quantity +
                " - " +
                (shipment.itemType === "Box"
                  ? shipment.itemType + "(es)"
                  : shipment.itemType + "(s)")}
            </Text>
          </View>
          <View
            style={{
              display: "flex",
              gap: 4,
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Text style={{ fontSize: 17 }}>Dimensions:</Text>
            <Text style={{ fontSize: 17 }}>{getShipmentsDimensions(shipment, "inch")}</Text>
          </View>
          <View
            style={{
              display: "flex",
              flexDirection: "row",
              gap: 4,
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Text style={{ fontSize: 17 }}>Weight:</Text>
            <Text style={{ fontSize: 17 }}>{getShipmentsWeight(shipment, "lb")}</Text>
          </View>
        </View>
      </View>
    </View>
  );
}

function getShipmentsDimensions(shipment: Shipment, unitParam: string) {
  // Assume that all the dimension values are initially in "Inch"
  let { w, l, h } = shipment.measurement.dimension ?? { w: 0, h: 0, l: 0 };
  let unit = "(in)";
  switch (unitParam) {
    case "feet":
      // 12 inch = 1 feet
      w = w * 12;
      h = h * 12;
      l = l * 12;
      unit = "(ft)";
      break;
    case "meter":
      // 39.37007874 inch = 1 meter
      w = w * 39.37007874;
      h = h * 39.37007874;
      l = l * 39.37007874;
      unit = "(m)";
      break;
    case "centimeter":
      // 0.3937007874 inch = 1 centimeter
      w = w * 0.3937007874;
      h = h * 0.3937007874;
      l = l * 0.3937007874;
      unit = "(cm)";
      break;
    default:
      // nothing to do
      break;
  }
  const formatter = new Intl.NumberFormat("en-US");
  return (
    formatter.format(w) +
    " x " +
    formatter.format(h) +
    " x " +
    formatter.format(l) +
    " " +
    unit
  );
}

function getShipmentsWeight(shipment: Shipment, unitParam: string) {
  // Assume that the weight is initially in "Pound - lb" //should be "lbs" though
  let w = shipment.measurement.weight?.w ?? 0;
  let unit = "(lbs)";
  switch (unitParam) {
    case "kg":
      // 2.2046226218 lbs = 1 kg
      w *= 2.2046226218;
      unit = "(kg)";
      break;
    case "ton":
      // 2204.6226218 lbs = 1 ton
      w *= 2204.6226218;
      unit = "(t)";
      break;
    default:
      // nothing to do
      break;
  }

  const formatter = new Intl.NumberFormat("en-US");
  return formatter.format(w) + " " + unit;
}
