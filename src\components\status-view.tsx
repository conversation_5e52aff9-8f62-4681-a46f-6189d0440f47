import React, { useState } from "react";
import { ActivityIndicator, <PERSON><PERSON>, Card, MD2Colors, Text } from "react-native-paper";
import { FontAwesome6 } from "@expo/vector-icons";
import { LoadStatusType, MarketLoad } from "@core/models/load-market.model";
import { View } from "react-native";
import BOLFormView from "./bol-form";
import { BolInfo } from "@core/models/bolInfo.model";

export default function StatusView({
  load,
  click,
}: {
  load: MarketLoad;
  click: (data: BolInfo) => void;
}) {
  const [open, setOpen] = useState(false);
  const shouldDropOff = (data: MarketLoad): boolean => {
    return (
      data.status === LoadStatusType.IN_TRANSIT &&
      data.shipment.routes[0].shipper.status === "ACCEPTED"
    );
  };

  const viewAction = (
    id: number,
    label?: string,
    description?: string,
    icon?: string,
    color?: string
  ) => {
    return (
      <>
        <Card>
          <Card.Content>
            {description && (
              <View
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 4,
                  alignItems: "center",
                  marginTop: 16,
                }}
              >
                {icon && id !== 0 && (
                  <FontAwesome6
                    name={icon}
                    size={36}
                    color={color ?? "#e9692e"}
                  />
                )}
                {id === 0 && (
                  <ActivityIndicator
                    animating={true}
                    size={"large"}
                    color={MD2Colors.orange600}
                  />
                )}
                <Text style={{ alignSelf: "center", fontSize: 18 }}>
                  {description}
                </Text>
              </View>
            )}
            {label && (
              <Button
                mode="contained"
                style={{ width: 180, alignSelf: "center", marginVertical: 16 }}
                onPress={() => setOpen(true)}
              >
                {label}
              </Button>
            )}
          </Card.Content>
        </Card>
        <BOLFormView
          label={
            shouldDropOff(load)
              ? load.shipment.routes[0].receiver.name
              : load.shipment.routes[0].shipper.name
          }
          dropOffFlow={shouldDropOff(load)}
          show={open}
          close={() => setOpen(false)}
          save={(data) =>
            click({
              ...data,
              id,
            })
          }
        />
      </>
    );
  };

  switch (load.status) {
    case LoadStatusType.ASSIGNED:
      return viewAction(
        1,
        "Accept",
        `Ready for the ride! To get started click to "Accept", and proceed shipment tracking.`,
        "person-circle-check",
        "navy"
      );
    case LoadStatusType.CHECKED_IN:
      return viewAction(
        2,
        "Pick-up Load",
        `Ready for the ride! Proceed with your shipment tracking.`,
        "trailer",
        "green"
      );
    case LoadStatusType.IN_TRANSIT:
      return viewAction(
        3,
        "Drop-off Load",
        `Great job! You're almost there. Ready to deliver your shipment?`,
        "building-circle-arrow-right",
        "e9692e"
      );
    case LoadStatusType.REVIEW:
      return viewAction(
        0,
        undefined,
        `Excellent! Thank for submitting your shipment. Your load details have been submitted. \n Awaiting approval, kindly wait for our response. We will get back to you soon.`,
        "magnifying-glass-location",
        "#31722C"
      );

    default:
      return null;
  }
}
