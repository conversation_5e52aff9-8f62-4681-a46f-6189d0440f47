{"expo": {"name": "1-Way Express Driver Tracker", "slug": "owe-tracking", "version": "1.0.39", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"UIBackgroundModes": ["location", "fetch", "remote-notification"], "CFBundleAllowMixedLocalizations": true, "NSLocationAlwaysAndWhenInUseUsageDescription": "1-Way Express Driver App requires geolocation to improve the quality of the service", "NSLocationAlwaysUsageDescription": "1-Way Express Driver App requires geolocation to improve the quality of the service", "NSLocationWhenInUseUsageDescription": "1-Way Express Driver App requires geolocation to improve the quality of the service", "NSCameraUsageDescription": "The picture will be used to inform the state of the freight during delivery", "NSUserNotificationsUsageDescription": "Allow notifications to stay up to date. These can be configured from the System Settings.", "NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true, "NSAllowsLocalNetworking": true}}, "bundleIdentifier": "com.kaizenerds.oweDriverMobileAppTracker", "buildNumber": "12"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.FOREGROUND_SERVICE", "android.permission.FOREGROUND_SERVICE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_BACKGROUND_LOCATION", "android.permission.CAMERA", "android.permission.INTERNET"], "versionCode": 18, "package": "com.kaizenerds.owe_driver_mobile1"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-sqlite", ["expo-image-picker", {"photosPermission": "Allow $(PRODUCT_NAME) to use your camero or photos."}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location OWE II.", "isIosBackgroundLocationEnabled": true, "isAndroidBackgroundLocationEnabled": true, "isAndroidForegroundServiceEnabled": true}], ["@sentry/react-native/expo", {"url": "https://sentry.io/", "note": "Use SENTRY_AUTH_TOKEN env to authenticate with Sen<PERSON>.", "project": "owe-drivertracker", "organization": "kaizenerds"}], ["expo-build-properties", {"android": {"enableProguardInReleaseBuilds": true}}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "93c5dfa2-53ca-436d-b3f3-1b0b05bc989a"}}, "owner": "zendev01", "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/93c5dfa2-53ca-436d-b3f3-1b0b05bc989a"}}}