import { createContext, useContext, useEffect, useState } from "react";
import { LiveData, StorageKeys, Strategy } from "@core/models/common";
import * as Location from "expo-location";
import { getFromCache, storeInCache } from "@utils/app.settings";
import { Tracking } from "@core/models/tracking.model";
import * as Sentry from "@sentry/react-native";
import { AuthnService } from "@core/services/auth.service";
import { LoadStatusType, MarketLoad } from "@core/models/load-market.model";
import { useRouter } from "expo-router";

interface MainContextValue {
  data: LiveData;
  setStrategy: (value: LiveData) => Promise<void>;
  clearStrategy: () => Promise<void>;
}
interface ProviderProps {
  children: React.ReactNode;
}

const MainContext = createContext<MainContextValue | undefined>(undefined);

export function StrateyProvider(props: ProviderProps) {
  const [status] = Location.useForegroundPermissions();
  const [data, setData] = useState<LiveData>({
    locationGranted: status?.status === Location.PermissionStatus.GRANTED,
    type: Strategy.SIGIN,
  });
  const router = useRouter();

  useEffect(() => {
    (async () => {
      try {
        const permissionStatus = await Location.useForegroundPermissions();
        if (permissionStatus[0]?.status === Location.PermissionStatus.GRANTED) {
          setData(
            await getStrategy({
              locationGranted: true,
              type: Strategy.SIGIN,
            })
          );
          return;
        }
        const getStatus = await permissionStatus[2]();
        setData(
          await getStrategy({
            locationGranted:
              getStatus.status === Location.PermissionStatus.GRANTED,
            type: Strategy.SIGIN,
          })
        );
      } catch (error) {
        Sentry.captureException(error, {
          extra: {
            message: "Unable to get driver location",
            loadId: data.load?.id,
            proId: data.load?.shipment.proId,
            tracking: data.tracking?.id,
            driver: data.load?.driver?.name,
          },
        });
      }
    })();

  }, []);

  useEffect(() => {
    if (!status?.status) return;

    const timeout = setTimeout(() => {
      if (status?.status !== Location.PermissionStatus.GRANTED) {
        router.push("/disclosure");
        return;
      }

      if (
        data.type === Strategy.DONE ||
        data.load?.status === LoadStatusType.DELIVERED
      ) {
        router.push("/complete");
      }
      if (data.type === Strategy.LIVE) {
        router.push("/home");
      }
      if (![Strategy.DONE, Strategy.LIVE].includes(data.type) || !data?.load) {
        router.push("/signIn");
      }
    }, 100);

    return () => clearTimeout(timeout);
  }, [status]);

  const getStrategyType = async (data: LiveData): Promise<LiveData> => {
    if (!data.load) {
      const load = (await getFromCache(StorageKeys.LOAD)) as MarketLoad;
      const tracking = (await getFromCache(StorageKeys.TRACKING)) as Tracking;
      data = { ...data, load, tracking };
    }

    if (!data.load || !data.tracking) {
      data = { ...data, type: Strategy.SIGIN };
      return data;
    }

    if (data.load?.isLocked) {
      data = { ...data, type: Strategy.DONE };
      return data;
    }

    if (data.load?.status === LoadStatusType.DELIVERED) {
      data = { ...data, type: Strategy.DONE };
      return data;
    }
    data = { ...data, type: Strategy.LIVE };
    return data;
  };

  const getStrategy = async ({
    load,
    tracking,
    locationGranted,
    type,
  }: LiveData) => {
    let data = { load, tracking, locationGranted, type } as LiveData;

    if (load) {
      await storeInCache(StorageKeys.LOAD, load);
      data = await getStrategyType({ ...data, load: load });
    }
    if (tracking) {
      await storeInCache(StorageKeys.TRACKING, tracking);
      data = await getStrategyType({ ...data, tracking });
    }
    if (locationGranted) {
      data = await getStrategyType({ ...data, locationGranted });
    }
    return data;
  };

  const updateData = async (data: LiveData): Promise<void> => {
    const item = await getStrategy(data);
    setData(item);
  };

  const cleanUpData = async (): Promise<void> => {
    await AuthnService.signOut(data.load!);
    setData({
      ...data,
      load: undefined,
      tracking: undefined,
      type: Strategy.SIGIN,
    });
  };

  return (
    <MainContext.Provider
      value={{
        data,
        setStrategy: updateData,
        clearStrategy: cleanUpData,
      }}
    >
      {props.children}
    </MainContext.Provider>
  );
}

export const useStrategy = () => {
  const context = useContext(MainContext);

  if (!context) {
    throw new Error("useAuth must be used within an Main ContextProvider");
  }

  return context;
};
