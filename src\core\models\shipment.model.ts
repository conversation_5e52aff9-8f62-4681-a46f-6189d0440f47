export enum ShipmentStatusType {
  CREATED = 'CREATED',
  'CHECKED_IN' = 'CHECKED_IN',
  'IN_TRANSIT' = 'IN_TRANSIT',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED',
  SOLD = 'SOLD',
}

export interface Shipment {
  id: string;
  updatedAt?: string;
  updatedName?: string;
  updatedBy?: string;
  proId: string;
  clientProId: string;
  lookupKeyword?: string;
  itemType: string;
  quantity: number;
  description: string;
  notes: string;
  status: ShipmentStatusType;
  measurement: {
    weight: { w: number; unity: Unity };
    dimension: {
      l: number;
      w: number;
      h: number;
      unity: Unity;
    };
  };
  client: {
    id: string;
    name: string;
  };
  routes: ShipmentRoute[];
  broker: string;
  finance?: {
    purchaseAmount: number;
  };
}

export interface ShipmentRoute {
  order: number;
  shipper: RoutePoint;
  receiver: RoutePoint;
  status: 'PENDING' | 'COMPLETED' | 'NONE';
  lastTrackingChangeDate?: string;
}

export type RoutePoint = {
  name: string;
  address: Address;
  date: string;
  status: 'NONE' | 'DECLINED' | 'ACCEPTED';
  lastTrackingChangeDate?: string;
};
export interface Address {
  fullAddress: string;
  suite?: string;
  coordinate?: {
    lat: number;
    lng: number;
  };
}
export type Unity = {
  name?: string;
  short?: string;
  toBase?: number;
  fromBase?: number;
};
// Example of a Shipment object
// 
const exampleShipment: Shipment = {
  id: "ship_123",  proId: "PRO123",
  clientProId: "CLIENT_PRO123",  lookupKeyword: "ELECTRONICS_BATCH",
  itemType: "Electronics",  quantity: 5,
  description: "Laptop computers",  notes: "Handle with care",
  status: ShipmentStatusType.IN_TRANSIT,  measurement: {
    weight: { w: 25, unity: { name: "Kilogram", short: "kg" } },    dimension: {
      l: 60,      w: 40,
      h: 20,      unity: { name: "Centimeter", short: "cm" }
    }  },
  client: {
    id: "client_456",    name: "Tech Solutions Inc"  },
  routes: [    {
      order: 1,      shipper: {
        name: "Main Warehouse",        address: {
          fullAddress: "123 Warehouse St, Chicago, IL",          suite: "Unit 5",
          coordinate: { lat: 41.8781, lng: -87.6298 }        },
        date: "2023-11-01T10:00:00Z",
        status: "ACCEPTED",        lastTrackingChangeDate: "2023-11-01T10:30:00Z"      },
      receiver: {        name: "Distribution Center",
        address: {
          fullAddress: "456 Center Ave, Detroit, MI",          coordinate: { lat: 42.3314, lng: -83.0458 }        },
        date: "2023-11-03T15:00:00Z",        status: "NONE"
      },
      status: "PENDING",      lastTrackingChangeDate: "2023-11-01T10:30:00Z"    }
  ],  broker: "Fast Shipping LLC",
  finance: {    purchaseAmount: 15000
  },  updatedAt: "2023-11-01T10:30:00Z",
  updatedName: "John Doe",
  updatedBy: "user_789"
};




























