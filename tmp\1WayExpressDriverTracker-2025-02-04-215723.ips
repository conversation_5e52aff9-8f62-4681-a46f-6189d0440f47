{"roots_installed":0,"app_cohort":"2|date=1738726200000&sf=143441&tid=844508ca90bd1faabd6af8f389bef320fcd78e4808970797109778374c7aac60&ttype=i","app_name":"1WayExpressDriverTracker","app_version":"1.0.37","timestamp":"2025-02-04 21:57:23.00 -0600","slice_uuid":"acaec34a-6577-33e5-b48e-beb2af392947","adam_id":"1671040089","build_version":"10","platform":2,"bundleID":"com.kaizenerds.oweDriverMobileAppTracker","share_with_app_devs":1,"is_first_party":0,"bug_type":"309","os_version":"iPhone OS 18.1.1 (22B91)","incident_id":"CAE7E1DB-B0E3-4B80-8799-E16E7C3FA32E","name":"1WayExpressDriverTracker","is_beta":1}
{
  "uptime" : 800000,
  "procRole" : "Foreground",
  "version" : 2,
  "userID" : 501,
  "deployVersion" : 210,
  "modelCode" : "iPhone15,4",
  "coalitionID" : 9813,
  "osVersion" : {
    "isEmbedded" : true,
    "train" : "iPhone OS 18.1.1",
    "releaseType" : "User",
    "build" : "22B91"
  },
  "captureTime" : "2025-02-04 21:57:22.9769 -0600",
  "codeSigningMonitor" : 2,
  "incident" : "CAE7E1DB-B0E3-4B80-8799-E16E7C3FA32E",
  "pid" : 32852,
  "translated" : false,
  "cpuType" : "ARM-64",
  "roots_installed" : 0,
  "bug_type" : "309",
  "procLaunch" : "2025-02-04 21:47:16.8263 -0600",
  "procStartAbsTime" : 19261142024207,
  "procExitAbsTime" : 19275689385017,
  "procName" : "1WayExpressDriverTracker",
  "procPath" : "\/private\/var\/containers\/Bundle\/Application\/A6F92D23-D205-4824-8686-F399A1E1FA49\/1WayExpressDriverTracker.app\/1WayExpressDriverTracker",
  "bundleInfo" : {"CFBundleShortVersionString":"1.0.37","CFBundleVersion":"10","CFBundleIdentifier":"com.kaizenerds.oweDriverMobileAppTracker","DTAppStoreToolsBuild":"16C5031b"},
  "storeInfo" : {"itemID":"1671040089","storeCohortMetadata":"2|date=1738726200000&sf=143441&tid=844508ca90bd1faabd6af8f389bef320fcd78e4808970797109778374c7aac60&ttype=i","entitledBeta":true,"deviceIdentifierForVendor":"B7C43522-C328-4EF5-9739-607C8BDACEF5","distributorID":"com.apple.TestFlight","softwareVersionExternalIdentifier":"162812850","applicationVariant":"1:iPhone15,4:18","thirdParty":true},
  "parentProc" : "launchd",
  "parentPid" : 1,
  "coalitionName" : "com.kaizenerds.oweDriverMobileAppTracker",
  "isBeta" : 1,
  "appleIntelligenceStatus" : {"state":"unavailable","reasons":["notOptedIn","deviceNotCapable","assetIsNotReady","siriAssetIsNotReady","unableToFetchAvailability"]},
  "wasUnlockedSinceBoot" : 1,
  "isLocked" : 0,
  "codeSigningID" : "com.kaizenerds.oweDriverMobileAppTracker",
  "codeSigningTeamID" : "49YH36GGZ5",
  "codeSigningFlags" : 570434305,
  "codeSigningValidationCategory" : 2,
  "codeSigningTrustLevel" : 4,
  "instructionByteStream" : {"beforePC":"fyMD1f17v6n9AwCRPOz\/l78DAJH9e8Go\/w9f1sADX9YQKYDSARAA1A==","atPC":"AwEAVH8jA9X9e7+p\/QMAkTHs\/5e\/AwCR\/XvBqP8PX9bAA1\/WECeA0g=="},
  "bootSessionUUID" : "2C332D67-72C4-4C70-90F0-63C0C1F913A6",
  "basebandVersion" : "2.20.03",
  "exception" : {"codes":"0x0000000000000000, 0x0000000000000000","rawCodes":[0,0],"type":"EXC_CRASH","signal":"SIGABRT"},
  "termination" : {"flags":0,"code":6,"namespace":"SIGNAL","indicator":"Abort trap: 6","byProc":"1WayExpressDriverTracker","byPid":32852},
  "asi" : {"libsystem_c.dylib":["abort() called"]},
  "faultingThread" : 7,
  "threads" : [{"id":7976282,"threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592,"symbolLocation":24,"symbol":"OBJC_CLASS_$_IPAMutableRectArray"},{"value":69282117451776},{"value":0},{"value":69282117451776},{"value":2},{"value":4294967295},{"value":18446744073709550527},{"value":2},{"value":0},{"value":0},{"value":0},{"value":16131},{"value":11776},{"value":0},{"value":18446744073709551569},{"value":7031726936,"symbolLocation":56,"symbol":"clock_gettime"},{"value":0},{"value":4294967295},{"value":2},{"value":69282117451776},{"value":0},{"value":69282117451776},{"value":6136964952},{"value":8589934592,"symbolLocation":24,"symbol":"OBJC_CLASS_$_IPAMutableRectArray"},{"value":21592279046},{"value":21592279046},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":8246467992},"cpsr":{"value":4096},"fp":{"value":6136964800},"sp":{"value":6136964720},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":8246453896},"far":{"value":0}},"queue":"com.apple.main-thread","frames":[{"imageOffset":5768,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":4},{"imageOffset":19864,"symbol":"mach_msg2_internal","symbolLocation":80,"imageIndex":4},{"imageOffset":19632,"symbol":"mach_msg_overwrite","symbolLocation":424,"imageIndex":4},{"imageOffset":19196,"symbol":"mach_msg","symbolLocation":24,"imageIndex":4},{"imageOffset":342660,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":160,"imageIndex":5},{"imageOffset":340272,"symbol":"__CFRunLoopRun","symbolLocation":1212,"imageIndex":5},{"imageOffset":337968,"symbol":"CFRunLoopRunSpecific","symbolLocation":588,"imageIndex":5},{"imageOffset":4548,"symbol":"GSEventRunModal","symbolLocation":164,"imageIndex":6},{"imageOffset":4009648,"symbol":"-[UIApplication _run]","symbolLocation":816,"imageIndex":7},{"imageOffset":4724148,"symbol":"UIApplicationMain","symbolLocation":340,"imageIndex":7},{"imageOffset":33948,"imageIndex":0},{"imageOffset":212680,"symbol":"start","symbolLocation":2724,"imageIndex":8}]},{"id":7976295,"name":"com.apple.uikit.eventfetch-thread","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592,"symbolLocation":24,"symbol":"OBJC_CLASS_$_IPAMutableRectArray"},{"value":90172838379520},{"value":0},{"value":90172838379520},{"value":2},{"value":4294967295},{"value":18446744073709550527},{"value":2},{"value":0},{"value":0},{"value":0},{"value":20995},{"value":0},{"value":0},{"value":18446744073709551569},{"value":7031726936,"symbolLocation":56,"symbol":"clock_gettime"},{"value":0},{"value":4294967295},{"value":2},{"value":90172838379520},{"value":0},{"value":90172838379520},{"value":6142107000},{"value":8589934592,"symbolLocation":24,"symbol":"OBJC_CLASS_$_IPAMutableRectArray"},{"value":21592279046},{"value":21592279046},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":8246467992},"cpsr":{"value":4096},"fp":{"value":6142106848},"sp":{"value":6142106768},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":8246453896},"far":{"value":0}},"frames":[{"imageOffset":5768,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":4},{"imageOffset":19864,"symbol":"mach_msg2_internal","symbolLocation":80,"imageIndex":4},{"imageOffset":19632,"symbol":"mach_msg_overwrite","symbolLocation":424,"imageIndex":4},{"imageOffset":19196,"symbol":"mach_msg","symbolLocation":24,"imageIndex":4},{"imageOffset":342660,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":160,"imageIndex":5},{"imageOffset":340272,"symbol":"__CFRunLoopRun","symbolLocation":1212,"imageIndex":5},{"imageOffset":337968,"symbol":"CFRunLoopRunSpecific","symbolLocation":588,"imageIndex":5},{"imageOffset":750848,"symbol":"-[NSRunLoop(NSRunLoop) runMode:beforeDate:]","symbolLocation":212,"imageIndex":11},{"imageOffset":750416,"symbol":"-[NSRunLoop(NSRunLoop) runUntilDate:]","symbolLocation":64,"imageIndex":11},{"imageOffset":4088664,"symbol":"-[UIEventFetcher threadMain]","symbolLocation":420,"imageIndex":7},{"imageOffset":820936,"symbol":"__NSThread__start__","symbolLocation":724,"imageIndex":11},{"imageOffset":25468,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":12},{"imageOffset":5268,"symbol":"thread_start","symbolLocation":8,"imageIndex":12}]},{"id":7976300,"name":"com.facebook.react.runtime.JavaScript","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592,"symbolLocation":24,"symbol":"OBJC_CLASS_$_IPAMutableRectArray"},{"value":133088151601152},{"value":0},{"value":133088151601152},{"value":2},{"value":4294967295},{"value":18446744073709550527},{"value":2},{"value":0},{"value":0},{"value":0},{"value":30987},{"value":9216},{"value":0},{"value":18446744073709551569},{"value":7031726936,"symbolLocation":56,"symbol":"clock_gettime"},{"value":0},{"value":4294967295},{"value":2},{"value":133088151601152},{"value":0},{"value":133088151601152},{"value":6143827384},{"value":8589934592,"symbolLocation":24,"symbol":"OBJC_CLASS_$_IPAMutableRectArray"},{"value":21592279046},{"value":21592279046},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":8246467992},"cpsr":{"value":4096},"fp":{"value":6143827232},"sp":{"value":6143827152},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":8246453896},"far":{"value":0}},"frames":[{"imageOffset":5768,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":4},{"imageOffset":19864,"symbol":"mach_msg2_internal","symbolLocation":80,"imageIndex":4},{"imageOffset":19632,"symbol":"mach_msg_overwrite","symbolLocation":424,"imageIndex":4},{"imageOffset":19196,"symbol":"mach_msg","symbolLocation":24,"imageIndex":4},{"imageOffset":342660,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":160,"imageIndex":5},{"imageOffset":340272,"symbol":"__CFRunLoopRun","symbolLocation":1212,"imageIndex":5},{"imageOffset":337968,"symbol":"CFRunLoopRunSpecific","symbolLocation":588,"imageIndex":5},{"imageOffset":5203536,"imageIndex":0},{"imageOffset":820936,"symbol":"__NSThread__start__","symbolLocation":724,"imageIndex":11},{"imageOffset":25468,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":12},{"imageOffset":5268,"symbol":"thread_start","symbolLocation":8,"imageIndex":12}]},{"id":7976301,"name":"hades","threadState":{"x":[{"value":260},{"value":0},{"value":0},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6144405160},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8719515752},{"value":0},{"value":12924653696},{"value":12924653760},{"value":6144405728},{"value":0},{"value":0},{"value":0},{"value":1},{"value":256},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":9187486288},"cpsr":{"value":1610616832},"fp":{"value":6144405280},"sp":{"value":6144405136},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":8246476688},"far":{"value":0}},"frames":[{"imageOffset":28560,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":4},{"imageOffset":14928,"symbol":"_pthread_cond_wait","symbolLocation":1204,"imageIndex":12},{"imageOffset":136580,"symbol":"std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&)","symbolLocation":28,"imageIndex":13},{"imageOffset":839160,"symbol":"hermes::vm::HadesGC::Executor::worker()","symbolLocation":116,"imageIndex":2},{"imageOffset":839008,"symbol":"void* std::__1::__thread_proxy[abi:v160006]<std::__1::tuple<std::__1::unique_ptr<std::__1::__thread_struct, std::__1::default_delete<std::__1::__thread_struct>>, hermes::vm::HadesGC::Executor::Executor()::'lambda'()>>(void*)","symbolLocation":44,"imageIndex":2},{"imageOffset":25468,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":12},{"imageOffset":5268,"symbol":"thread_start","symbolLocation":8,"imageIndex":12}]},{"id":7976303,"name":"hades","threadState":{"x":[{"value":260},{"value":0},{"value":0},{"value":0},{"value":0},{"value":160},{"value":0},{"value":0},{"value":6144978600},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":8719515752},{"value":0},{"value":12924691920},{"value":12924691984},{"value":6144979168},{"value":0},{"value":0},{"value":0},{"value":1},{"value":256},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":9187486288},"cpsr":{"value":1610616832},"fp":{"value":6144978720},"sp":{"value":6144978576},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":8246476688},"far":{"value":0}},"frames":[{"imageOffset":28560,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":4},{"imageOffset":14928,"symbol":"_pthread_cond_wait","symbolLocation":1204,"imageIndex":12},{"imageOffset":136580,"symbol":"std::__1::condition_variable::wait(std::__1::unique_lock<std::__1::mutex>&)","symbolLocation":28,"imageIndex":13},{"imageOffset":839160,"symbol":"hermes::vm::HadesGC::Executor::worker()","symbolLocation":116,"imageIndex":2},{"imageOffset":839008,"symbol":"void* std::__1::__thread_proxy[abi:v160006]<std::__1::tuple<std::__1::unique_ptr<std::__1::__thread_struct, std::__1::default_delete<std::__1::__thread_struct>>, hermes::vm::HadesGC::Executor::Executor()::'lambda'()>>(void*)","symbolLocation":44,"imageIndex":2},{"imageOffset":25468,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":12},{"imageOffset":5268,"symbol":"thread_start","symbolLocation":8,"imageIndex":12}]},{"id":7984178,"frames":[{"imageOffset":5248,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":12}],"threadState":{"x":[{"value":6139817984},{"value":22359},{"value":6139281408},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6139817984},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":9187476608},"far":{"value":0}}},{"id":7984226,"frames":[{"imageOffset":5248,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":12}],"threadState":{"x":[{"value":6140964864},{"value":9531},{"value":6140428288},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6140964864},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":9187476608},"far":{"value":0}}},{"triggered":true,"id":7984275,"threadState":{"x":[{"value":0},{"value":0},{"value":0},{"value":0},{"value":18446744073709008314},{"value":26},{"value":2045},{"value":789120},{"value":10702454569915417009},{"value":10702454574963503537},{"value":127},{"value":1536},{"value":2043},{"value":2045},{"value":3911249933},{"value":3909150795},{"value":328},{"value":6138671104},{"value":0},{"value":6},{"value":62263},{"value":6138671328},{"value":276},{"value":6138671328},{"value":12948662248},{"value":0},{"value":0},{"value":12892275136},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":9187503864},"cpsr":{"value":1073745920},"fp":{"value":6138668912},"sp":{"value":6138668880},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":8246497748,"matchesCrashFrame":1},"far":{"value":0}},"queue":"com.meta.react.turbomodulemanager.queue","frames":[{"imageOffset":49620,"symbol":"__pthread_kill","symbolLocation":8,"imageIndex":4},{"imageOffset":32504,"symbol":"pthread_kill","symbolLocation":268,"imageIndex":12},{"imageOffset":490200,"symbol":"abort","symbolLocation":128,"imageIndex":10},{"imageOffset":79288,"symbol":"abort_message","symbolLocation":132,"imageIndex":14},{"imageOffset":7056,"symbol":"demangling_terminate_handler()","symbolLocation":320,"imageIndex":14},{"imageOffset":208420,"symbol":"_objc_terminate()","symbolLocation":172,"imageIndex":15},{"imageOffset":75900,"symbol":"std::__terminate(void (*)())","symbolLocation":16,"imageIndex":14},{"imageOffset":75808,"symbol":"std::terminate()","symbolLocation":108,"imageIndex":14},{"imageOffset":16612,"symbol":"_dispatch_client_callout","symbolLocation":40,"imageIndex":16},{"imageOffset":46808,"symbol":"_dispatch_lane_serial_drain","symbolLocation":744,"imageIndex":16},{"imageOffset":49632,"symbol":"_dispatch_lane_invoke","symbolLocation":380,"imageIndex":16},{"imageOffset":94808,"symbol":"_dispatch_root_queue_drain_deferred_wlh","symbolLocation":288,"imageIndex":16},{"imageOffset":92836,"symbol":"_dispatch_workloop_worker_thread","symbolLocation":540,"imageIndex":16},{"imageOffset":19580,"symbol":"_pthread_wqthread","symbolLocation":288,"imageIndex":12},{"imageOffset":5256,"symbol":"start_wqthread","symbolLocation":8,"imageIndex":12}]},{"id":7984276,"name":"com.apple.NSURLConnectionLoader","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592,"symbolLocation":24,"symbol":"OBJC_CLASS_$_IPAMutableRectArray"},{"value":245341416849408},{"value":0},{"value":245341416849408},{"value":2},{"value":4294967295},{"value":18446744073709550527},{"value":2},{"value":0},{"value":0},{"value":0},{"value":57123},{"value":3328},{"value":0},{"value":18446744073709551569},{"value":7031726936,"symbolLocation":56,"symbol":"clock_gettime"},{"value":0},{"value":4294967295},{"value":2},{"value":245341416849408},{"value":0},{"value":245341416849408},{"value":6140386616},{"value":8589934592,"symbolLocation":24,"symbol":"OBJC_CLASS_$_IPAMutableRectArray"},{"value":21592279046},{"value":21592279046},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":8246467992},"cpsr":{"value":4096},"fp":{"value":6140386464},"sp":{"value":6140386384},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":8246453896},"far":{"value":0}},"frames":[{"imageOffset":5768,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":4},{"imageOffset":19864,"symbol":"mach_msg2_internal","symbolLocation":80,"imageIndex":4},{"imageOffset":19632,"symbol":"mach_msg_overwrite","symbolLocation":424,"imageIndex":4},{"imageOffset":19196,"symbol":"mach_msg","symbolLocation":24,"imageIndex":4},{"imageOffset":342660,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":160,"imageIndex":5},{"imageOffset":340272,"symbol":"__CFRunLoopRun","symbolLocation":1212,"imageIndex":5},{"imageOffset":337968,"symbol":"CFRunLoopRunSpecific","symbolLocation":588,"imageIndex":5},{"imageOffset":1040096,"symbol":"+[__CFN_CoreSchedulingSetRunnable _run:]","symbolLocation":416,"imageIndex":17},{"imageOffset":820936,"symbol":"__NSThread__start__","symbolLocation":724,"imageIndex":11},{"imageOffset":25468,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":12},{"imageOffset":5268,"symbol":"thread_start","symbolLocation":8,"imageIndex":12}]},{"id":7984295,"name":"com.facebook.SocketRocket.NetworkThread","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592,"symbolLocation":24,"symbol":"OBJC_CLASS_$_IPAMutableRectArray"},{"value":369448791834624},{"value":0},{"value":369448791834624},{"value":2},{"value":4294967295},{"value":18446744073709550527},{"value":2},{"value":0},{"value":0},{"value":0},{"value":86019},{"value":512},{"value":0},{"value":18446744073709551569},{"value":7031726936,"symbolLocation":56,"symbol":"clock_gettime"},{"value":0},{"value":4294967295},{"value":2},{"value":369448791834624},{"value":0},{"value":369448791834624},{"value":6141533528},{"value":8589934592,"symbolLocation":24,"symbol":"OBJC_CLASS_$_IPAMutableRectArray"},{"value":21592279046},{"value":21592279046},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":8246467992},"cpsr":{"value":4096},"fp":{"value":6141533376},"sp":{"value":6141533296},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":8246453896},"far":{"value":0}},"frames":[{"imageOffset":5768,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":4},{"imageOffset":19864,"symbol":"mach_msg2_internal","symbolLocation":80,"imageIndex":4},{"imageOffset":19632,"symbol":"mach_msg_overwrite","symbolLocation":424,"imageIndex":4},{"imageOffset":19196,"symbol":"mach_msg","symbolLocation":24,"imageIndex":4},{"imageOffset":342660,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":160,"imageIndex":5},{"imageOffset":340272,"symbol":"__CFRunLoopRun","symbolLocation":1212,"imageIndex":5},{"imageOffset":337968,"symbol":"CFRunLoopRunSpecific","symbolLocation":588,"imageIndex":5},{"imageOffset":750848,"symbol":"-[NSRunLoop(NSRunLoop) runMode:beforeDate:]","symbolLocation":212,"imageIndex":11},{"imageOffset":6626852,"imageIndex":0},{"imageOffset":820936,"symbol":"__NSThread__start__","symbolLocation":724,"imageIndex":11},{"imageOffset":25468,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":12},{"imageOffset":5268,"symbol":"thread_start","symbolLocation":8,"imageIndex":12}]},{"id":7984297,"name":"com.apple.CFSocket.private","threadState":{"x":[{"value":4},{"value":0},{"value":12902518512},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":6142685408},{"value":0},{"value":9063429200},{"value":31},{"value":3587712},{"value":12948672256},{"value":72057602613770769,"symbolLocation":72057594037927937,"symbol":"OBJC_CLASS_$___NSCFArray"},{"value":8575842832,"symbolLocation":0,"symbol":"OBJC_CLASS_$___NSCFArray"},{"value":93},{"value":6900090844,"symbolLocation":0,"symbol":"-[__NSCFArray objectAtIndex:]"},{"value":0},{"value":32},{"value":8590355808,"symbolLocation":0,"symbol":"__CFActiveSocketsLock"},{"value":1},{"value":4436045824},{"value":21},{"value":12902518512},{"value":12902516704},{"value":8603891008,"symbolLocation":0,"symbol":"__kCFNull"},{"value":0},{"value":8590352384,"symbolLocation":8,"symbol":"OBJC_METACLASS_$___NSArrayReversed"}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6900767068},"cpsr":{"value":1610616832},"fp":{"value":6142685120},"sp":{"value":6142651344},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":8246485556},"far":{"value":0}},"frames":[{"imageOffset":37428,"symbol":"__select","symbolLocation":8,"imageIndex":4},{"imageOffset":768348,"symbol":"__CFSocketManager","symbolLocation":704,"imageIndex":5},{"imageOffset":25468,"symbol":"_pthread_start","symbolLocation":136,"imageIndex":12},{"imageOffset":5268,"symbol":"thread_start","symbolLocation":8,"imageIndex":12}]},{"id":7984394,"frames":[{"imageOffset":5248,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":12}],"threadState":{"x":[{"value":6139244544},{"value":72719},{"value":6138707968},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6139244544},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":9187476608},"far":{"value":0}}}],
  "usedImages" : [
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4329914368,
    "size" : 8962048,
    "uuid" : "acaec34a-6577-33e5-b48e-beb2af392947",
    "path" : "\/private\/var\/containers\/Bundle\/Application\/A6F92D23-D205-4824-8686-F399A1E1FA49\/1WayExpressDriverTracker.app\/1WayExpressDriverTracker",
    "name" : "1WayExpressDriverTracker"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4344184832,
    "size" : 606208,
    "uuid" : "d05154d8-7e81-3170-969b-c25ca4ee9ee2",
    "path" : "\/private\/var\/containers\/Bundle\/Application\/A6F92D23-D205-4824-8686-F399A1E1FA49\/1WayExpressDriverTracker.app\/Frameworks\/crsqlite.framework\/crsqlite",
    "name" : "crsqlite"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4350001152,
    "size" : 2080768,
    "uuid" : "aafe4813-46d6-3bf8-8568-a459ac4c6003",
    "path" : "\/private\/var\/containers\/Bundle\/Application\/A6F92D23-D205-4824-8686-F399A1E1FA49\/1WayExpressDriverTracker.app\/Frameworks\/hermes.framework\/hermes",
    "name" : "hermes"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4342251520,
    "size" : 49152,
    "uuid" : "35a44678-195b-39c2-bdd7-072893564b45",
    "path" : "\/private\/preboot\/Cryptexes\/OS\/usr\/lib\/libobjc-trampolines.dylib",
    "name" : "libobjc-trampolines.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 8246448128,
    "size" : 237556,
    "uuid" : "b9618c71-c0cb-31b6-825f-92a4737c890e",
    "path" : "\/usr\/lib\/system\/libsystem_kernel.dylib",
    "name" : "libsystem_kernel.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6899998720,
    "size" : 5517312,
    "uuid" : "1532d3d8-9b3b-3f2f-b35f-55a20ddf411b",
    "path" : "\/System\/Library\/Frameworks\/CoreFoundation.framework\/CoreFoundation",
    "name" : "CoreFoundation"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 8175267840,
    "size" : 36864,
    "uuid" : "8425ea11-000e-3e5e-8abc-bddf3ff3fa32",
    "path" : "\/System\/Library\/PrivateFrameworks\/GraphicsServices.framework\/GraphicsServices",
    "name" : "GraphicsServices"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6941835264,
    "size" : 32325632,
    "uuid" : "575e5140-fa6a-37c2-b00b-a4eacedfda53",
    "path" : "\/System\/Library\/PrivateFrameworks\/UIKitCore.framework\/UIKitCore",
    "name" : "UIKitCore"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 7531294720,
    "size" : 539040,
    "uuid" : "3060d36a-16ce-3c3a-9258-3881459f5714",
    "path" : "\/usr\/lib\/dyld",
    "name" : "dyld"
  },
  {
    "size" : 0,
    "source" : "A",
    "base" : 0,
    "uuid" : "00000000-0000-0000-0000-000000000000"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 7031701504,
    "size" : 524276,
    "uuid" : "0150f750-db0a-3f54-b23a-d21c55af8824",
    "path" : "\/usr\/lib\/system\/libsystem_c.dylib",
    "name" : "libsystem_c.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6879301632,
    "size" : 13688832,
    "uuid" : "6d0212cc-3b9e-32c9-be20-72989ce3acb8",
    "path" : "\/System\/Library\/Frameworks\/Foundation.framework\/Foundation",
    "name" : "Foundation"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 9187471360,
    "size" : 53236,
    "uuid" : "3ca98e38-8eee-3c26-9862-c5f66aad93c0",
    "path" : "\/usr\/lib\/system\/libsystem_pthread.dylib",
    "name" : "libsystem_pthread.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 7174742016,
    "size" : 581628,
    "uuid" : "491f481b-d014-381c-904e-aed69c09f984",
    "path" : "\/usr\/lib\/libc++.1.dylib",
    "name" : "libc++.1.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 9185386496,
    "size" : 110592,
    "uuid" : "5e1a3714-3fad-3ad7-a23d-61c4be170233",
    "path" : "\/usr\/lib\/libc++abi.dylib",
    "name" : "libc++abi.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6853066752,
    "size" : 331104,
    "uuid" : "1608892e-67db-3f94-9fc2-91492b86c95f",
    "path" : "\/usr\/lib\/libobjc.A.dylib",
    "name" : "libobjc.A.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 7031414784,
    "size" : 286720,
    "uuid" : "7de7ec03-cfb7-349d-9b9e-8782b38f231d",
    "path" : "\/usr\/lib\/system\/libdispatch.dylib",
    "name" : "libdispatch.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 6921826304,
    "size" : 3944448,
    "uuid" : "999c659a-fc7d-351f-a477-e97bbf2d8081",
    "path" : "\/System\/Library\/Frameworks\/CFNetwork.framework\/CFNetwork",
    "name" : "CFNetwork"
  }
],
  "sharedCache" : {
  "base" : 6852001792,
  "size" : 4300963840,
  "uuid" : "125d58c2-3989-3c68-9eaa-1958ca4ef620"
},
  "vmSummary" : "ReadOnly portion of Libraries: Total=1.6G resident=0K(0%) swapped_out_or_unallocated=1.6G(100%)\nWritable regions: Total=585.9M written=561K(0%) resident=561K(0%) swapped_out=0K(0%) unallocated=585.3M(100%)\n\n                                VIRTUAL   REGION \nREGION TYPE                        SIZE    COUNT (non-coalesced) \n===========                     =======  ======= \nActivity Tracing                   256K        1 \nCG raster data                    4000K       38 \nColorSync                          128K        5 \nCoreAnimation                     1536K       30 \nFoundation                          16K        1 \nKernel Alloc Once                   32K        1 \nMALLOC                           548.8M       18 \nMALLOC guard page                   32K        2 \nSQLite page cache                  512K        4 \nSTACK GUARD                        192K       12 \nStack                             6992K       12 \nVM_ALLOCATE                       26.2M       22 \n__AUTH                            6750K      548 \n__AUTH_CONST                      95.7M     1118 \n__CTF                               824        1 \n__DATA                            32.9M     1075 \n__DATA_CONST                      30.0M     1128 \n__DATA_DIRTY                      9910K     1008 \n__FONT_DATA                        2352        1 \n__INFO_FILTER                         8        1 \n__LINKEDIT                       198.3M        5 \n__OBJC_RW                         2946K        1 \n__TEXT                             1.4G     1146 \n__TPRO_CONST                       272K        2 \nlibnetwork                         128K        8 \nmapped file                      349.8M      104 \nowned unmapped memory             1152K        1 \npage table in kernel               561K        1 \nshared memory                       80K        4 \n===========                     =======  ======= \nTOTAL                              2.6G     6298 \n",
  "legacyInfo" : {
  "threadTriggered" : {
    "queue" : "com.meta.react.turbomodulemanager.queue"
  }
},
  "logWritingSignature" : "5445ed24c10a6ed048049b94d06e00bc3aed4529",
  "trialInfo" : {
  "rollouts" : [
    {
      "rolloutId" : "64c17a9925d75a7281053d4c",
      "factorPackIds" : {
        "SIRI_AUDIO_DISABLE_MEDIA_ENTITY_SYNC" : "64d29746ad29a465b3bbeace"
      },
      "deploymentId" : 240000002
    },
    {
      "rolloutId" : "61675b89201f677a9a4cbd65",
      "factorPackIds" : {
        "HEALTH_FEATURE_AVAILABILITY" : "674e17ae3238dd2541f0aa0d"
      },
      "deploymentId" : 240000164
    }
  ],
  "experiments" : [
    {
      "treatmentId" : "582596be-1d4a-408d-901b-5b311c006a4a",
      "experimentId" : "65f31ccb74b6f500a45abda4",
      "deploymentId" : 400000026
    }
  ]
}
}
