{"version": 3, "file": "graphql.web.mjs", "sources": ["../src/kind.js", "../src/error.ts", "../src/parser.ts", "../src/visitor.ts", "../src/printer.ts", "../src/values.ts", "../src/helpers.ts"], "sourcesContent": ["export const Kind = {\n  NAME: 'Name',\n  DOCUMENT: 'Document',\n  OPERATION_DEFINITION: 'OperationDefinition',\n  VARIABLE_DEFINITION: 'VariableDefinition',\n  SELECTION_SET: 'SelectionSet',\n  FIELD: 'Field',\n  ARGUMENT: 'Argument',\n  FRAGMENT_SPREAD: 'FragmentSpread',\n  INLINE_FRAGMENT: 'InlineFragment',\n  FRAGMENT_DEFINITION: 'FragmentDefinition',\n  VARIABLE: 'Variable',\n  INT: 'IntValue',\n  FLOAT: 'FloatValue',\n  STRING: 'StringValue',\n  BOOLEAN: 'BooleanValue',\n  NULL: 'NullValue',\n  ENUM: 'EnumValue',\n  LIST: 'ListValue',\n  OBJECT: 'ObjectValue',\n  OBJECT_FIELD: 'ObjectField',\n  DIRECTIVE: 'Directive',\n  NAMED_TYPE: 'NamedType',\n  LIST_TYPE: 'ListType',\n  NON_NULL_TYPE: 'NonNullType',\n\n  /*\n  SCHEMA_DEFINITION: 'SchemaDefinition',\n  OPERATION_TYPE_DEFINITION: 'OperationTypeDefinition',\n  SCALAR_TYPE_DEFINITION: 'ScalarTypeDefinition',\n  OBJECT_TYPE_DEFINITION: 'ObjectTypeDefinition',\n  FIELD_DEFINITION: 'FieldDefinition',\n  INPUT_VALUE_DEFINITION: 'InputValueDefinition',\n  INTERFACE_TYPE_DEFINITION: 'InterfaceTypeDefinition',\n  UNION_TYPE_DEFINITION: 'UnionTypeDefinition',\n  ENUM_TYPE_DEFINITION: 'EnumTypeDefinition',\n  ENUM_VALUE_DEFINITION: 'EnumValueDefinition',\n  INPUT_OBJECT_TYPE_DEFINITION: 'InputObjectTypeDefinition',\n  DIRECTIVE_DEFINITION: 'DirectiveDefinition',\n  SCHEMA_EXTENSION: 'SchemaExtension',\n  SCALAR_TYPE_EXTENSION: 'ScalarTypeExtension',\n  OBJECT_TYPE_EXTENSION: 'ObjectTypeExtension',\n  INTERFACE_TYPE_EXTENSION: 'InterfaceTypeExtension',\n  UNION_TYPE_EXTENSION: 'UnionTypeExtension',\n  ENUM_TYPE_EXTENSION: 'EnumTypeExtension',\n  INPUT_OBJECT_TYPE_EXTENSION: 'InputObjectTypeExtension',\n  */\n};\n\nexport const OperationTypeNode = {\n  QUERY: 'query',\n  MUTATION: 'mutation',\n  SUBSCRIPTION: 'subscription',\n};\n", "import type { Maybe, Extensions, Source } from './types';\nimport type { ASTNode } from './ast';\n\nexport class GraphQLError extends Error {\n  readonly locations: ReadonlyArray<any> | undefined;\n  readonly path: ReadonlyArray<string | number> | undefined;\n  readonly nodes: ReadonlyArray<any> | undefined;\n  readonly source: Source | undefined;\n  readonly positions: ReadonlyArray<number> | undefined;\n  readonly originalError: Error | undefined;\n  readonly extensions: Extensions;\n\n  constructor(\n    message: string,\n    nodes?: ReadonlyArray<ASTNode> | ASTNode | null,\n    source?: Maybe<Source>,\n    positions?: Maybe<ReadonlyArray<number>>,\n    path?: Maybe<ReadonlyArray<string | number>>,\n    originalError?: Maybe<Error>,\n    extensions?: Maybe<Extensions>\n  ) {\n    super(message);\n\n    this.name = 'GraphQLError';\n    this.message = message;\n\n    if (path) this.path = path;\n    if (nodes) this.nodes = (Array.isArray(nodes) ? nodes : [nodes]) as ASTNode[];\n    if (source) this.source = source;\n    if (positions) this.positions = positions;\n    if (originalError) this.originalError = originalError;\n\n    let _extensions = extensions;\n    if (!_extensions && originalError) {\n      const originalExtensions = (originalError as any).extensions;\n      if (originalExtensions && typeof originalExtensions === 'object') {\n        _extensions = originalExtensions;\n      }\n    }\n\n    this.extensions = _extensions || {};\n  }\n\n  toJSON(): any {\n    return { ...this, message: this.message };\n  }\n\n  toString(): string {\n    return this.message;\n  }\n\n  get [Symbol.toStringTag](): string {\n    return 'GraphQLError';\n  }\n}\n", "/**\n * This is a spec-compliant implementation of a GraphQL query language parser,\n * up-to-date with the October 2021 Edition. Unlike the reference implementation\n * in graphql.js it will only parse the query language, but not the schema\n * language.\n */\nimport type { Kind, OperationTypeNode } from './kind';\nimport { GraphQLError } from './error';\nimport type { Location, Source } from './types';\nimport type * as ast from './ast';\n\nlet input: string;\nlet idx: number;\n\nfunction error(kind: string) {\n  return new GraphQLError(`Syntax Error: Unexpected token at ${idx} in ${kind}`);\n}\n\nfunction advance(pattern: RegExp) {\n  pattern.lastIndex = idx;\n  if (pattern.test(input)) {\n    const match = input.slice(idx, (idx = pattern.lastIndex));\n    return match;\n  }\n}\n\nconst leadingRe = / +(?=[^\\s])/y;\nfunction blockString(string: string) {\n  const lines = string.split('\\n');\n  let out = '';\n  let commonIndent = 0;\n  let firstNonEmptyLine = 0;\n  let lastNonEmptyLine = lines.length - 1;\n  for (let i = 0; i < lines.length; i++) {\n    leadingRe.lastIndex = 0;\n    if (leadingRe.test(lines[i])) {\n      if (i && (!commonIndent || leadingRe.lastIndex < commonIndent))\n        commonIndent = leadingRe.lastIndex;\n      firstNonEmptyLine = firstNonEmptyLine || i;\n      lastNonEmptyLine = i;\n    }\n  }\n  for (let i = firstNonEmptyLine; i <= lastNonEmptyLine; i++) {\n    if (i !== firstNonEmptyLine) out += '\\n';\n    out += lines[i].slice(commonIndent).replace(/\\\\\"\"\"/g, '\"\"\"');\n  }\n  return out;\n}\n\n// Note: This is equivalent to: /(?:[\\s,]*|#[^\\n\\r]*)*/y\nfunction ignored() {\n  for (\n    let char = input.charCodeAt(idx++) | 0;\n    char === 9 /*'\\t'*/ ||\n    char === 10 /*'\\n'*/ ||\n    char === 13 /*'\\r'*/ ||\n    char === 32 /*' '*/ ||\n    char === 35 /*'#'*/ ||\n    char === 44 /*','*/ ||\n    char === 65279 /*'\\ufeff'*/;\n    char = input.charCodeAt(idx++) | 0\n  ) {\n    if (char === 35 /*'#'*/) while ((char = input.charCodeAt(idx++)) !== 10 && char !== 13);\n  }\n  idx--;\n}\n\nconst nameRe = /[_A-Za-z]\\w*/y;\n\n// NOTE: This should be compressed by our build step\n// This merges all possible value parsing into one regular expression\nconst valueRe = new RegExp(\n  '(?:' +\n    // `null`, `true`, and `false` literals (BooleanValue & NullValue)\n    '(null|true|false)|' +\n    // Variables starting with `$` then having a name (VariableNode)\n    '\\\\$(' +\n    nameRe.source +\n    ')|' +\n    // Numbers, starting with int then optionally following with a float part (IntValue and FloatValue)\n    '(-?\\\\d+)((?:\\\\.\\\\d+)?[eE][+-]?\\\\d+|\\\\.\\\\d+)?|' +\n    // Block strings starting with `\"\"\"` until the next unescaped `\"\"\"` (StringValue)\n    '(\"\"\"(?:\"\"\"|(?:[\\\\s\\\\S]*?[^\\\\\\\\])\"\"\"))|' +\n    // Strings starting with `\"` must be on one line (StringValue)\n    '(\"(?:\"|[^\\\\r\\\\n]*?[^\\\\\\\\]\"))|' + // string\n    // Enums are simply names except for our literals (EnumValue)\n    '(' +\n    nameRe.source +\n    '))',\n  'y'\n);\n\n// NOTE: Each of the groups above end up in the RegExpExecArray at the specified indices (starting with 1)\nconst enum ValueGroup {\n  Const = 1,\n  Var,\n  Int,\n  Float,\n  BlockString,\n  String,\n  Enum,\n}\n\ntype ValueExec = RegExpExecArray & {\n  [Prop in ValueGroup]: string | undefined;\n};\n\nconst complexStringRe = /\\\\/;\n\nfunction value(constant: true): ast.ConstValueNode;\nfunction value(constant: boolean): ast.ValueNode;\n\nfunction value(constant: boolean): ast.ValueNode {\n  let match: string | undefined;\n  let exec: ValueExec | null;\n  valueRe.lastIndex = idx;\n  if (input.charCodeAt(idx) === 91 /*'['*/) {\n    // Lists are checked ahead of time with `[` chars\n    idx++;\n    ignored();\n    const values: ast.ValueNode[] = [];\n    while (input.charCodeAt(idx) !== 93 /*']'*/) values.push(value(constant));\n    idx++;\n    ignored();\n    return {\n      kind: 'ListValue' as Kind.LIST,\n      values,\n    };\n  } else if (input.charCodeAt(idx) === 123 /*'{'*/) {\n    // Objects are checked ahead of time with `{` chars\n    idx++;\n    ignored();\n    const fields: ast.ObjectFieldNode[] = [];\n    while (input.charCodeAt(idx) !== 125 /*'}'*/) {\n      if ((match = advance(nameRe)) == null) throw error('ObjectField');\n      ignored();\n      if (input.charCodeAt(idx++) !== 58 /*':'*/) throw error('ObjectField');\n      ignored();\n      fields.push({\n        kind: 'ObjectField' as Kind.OBJECT_FIELD,\n        name: { kind: 'Name' as Kind.NAME, value: match },\n        value: value(constant),\n      });\n    }\n    idx++;\n    ignored();\n    return {\n      kind: 'ObjectValue' as Kind.OBJECT,\n      fields,\n    };\n  } else if ((exec = valueRe.exec(input) as ValueExec) != null) {\n    // Starting from here, the merged `valueRe` is used\n    idx = valueRe.lastIndex;\n    ignored();\n    if ((match = exec[ValueGroup.Const]) != null) {\n      return match === 'null'\n        ? { kind: 'NullValue' as Kind.NULL }\n        : {\n            kind: 'BooleanValue' as Kind.BOOLEAN,\n            value: match === 'true',\n          };\n    } else if ((match = exec[ValueGroup.Var]) != null) {\n      if (constant) {\n        throw error('Variable');\n      } else {\n        return {\n          kind: 'Variable' as Kind.VARIABLE,\n          name: {\n            kind: 'Name' as Kind.NAME,\n            value: match,\n          },\n        };\n      }\n    } else if ((match = exec[ValueGroup.Int]) != null) {\n      let floatPart: string | undefined;\n      if ((floatPart = exec[ValueGroup.Float]) != null) {\n        return {\n          kind: 'FloatValue' as Kind.FLOAT,\n          value: match + floatPart,\n        };\n      } else {\n        return {\n          kind: 'IntValue' as Kind.INT,\n          value: match,\n        };\n      }\n    } else if ((match = exec[ValueGroup.BlockString]) != null) {\n      return {\n        kind: 'StringValue' as Kind.STRING,\n        value: blockString(match.slice(3, -3)),\n        block: true,\n      };\n    } else if ((match = exec[ValueGroup.String]) != null) {\n      return {\n        kind: 'StringValue' as Kind.STRING,\n        // When strings don't contain escape codes, a simple slice will be enough, otherwise\n        // `JSON.parse` matches GraphQL's string parsing perfectly\n        value: complexStringRe.test(match) ? (JSON.parse(match) as string) : match.slice(1, -1),\n        block: false,\n      };\n    } else if ((match = exec[ValueGroup.Enum]) != null) {\n      return {\n        kind: 'EnumValue' as Kind.ENUM,\n        value: match,\n      };\n    }\n  }\n\n  throw error('Value');\n}\n\nfunction arguments_(constant: boolean): ast.ArgumentNode[] | undefined {\n  if (input.charCodeAt(idx) === 40 /*'('*/) {\n    const args: ast.ArgumentNode[] = [];\n    idx++;\n    ignored();\n    let _name: string | undefined;\n    do {\n      if ((_name = advance(nameRe)) == null) throw error('Argument');\n      ignored();\n      if (input.charCodeAt(idx++) !== 58 /*':'*/) throw error('Argument');\n      ignored();\n      args.push({\n        kind: 'Argument' as Kind.ARGUMENT,\n        name: { kind: 'Name' as Kind.NAME, value: _name },\n        value: value(constant),\n      });\n    } while (input.charCodeAt(idx) !== 41 /*')'*/);\n    idx++;\n    ignored();\n    return args;\n  }\n}\n\nfunction directives(constant: true): ast.ConstDirectiveNode[] | undefined;\nfunction directives(constant: boolean): ast.DirectiveNode[] | undefined;\n\nfunction directives(constant: boolean): ast.DirectiveNode[] | undefined {\n  if (input.charCodeAt(idx) === 64 /*'@'*/) {\n    const directives: ast.DirectiveNode[] = [];\n    let _name: string | undefined;\n    do {\n      idx++;\n      if ((_name = advance(nameRe)) == null) throw error('Directive');\n      ignored();\n      directives.push({\n        kind: 'Directive' as Kind.DIRECTIVE,\n        name: { kind: 'Name' as Kind.NAME, value: _name },\n        arguments: arguments_(constant),\n      });\n    } while (input.charCodeAt(idx) === 64 /*'@'*/);\n    return directives;\n  }\n}\n\nfunction type(): ast.TypeNode {\n  let match: string | undefined;\n  let lists = 0;\n  while (input.charCodeAt(idx) === 91 /*'['*/) {\n    lists++;\n    idx++;\n    ignored();\n  }\n  if ((match = advance(nameRe)) == null) throw error('NamedType');\n  ignored();\n  let type: ast.TypeNode = {\n    kind: 'NamedType' as Kind.NAMED_TYPE,\n    name: { kind: 'Name' as Kind.NAME, value: match },\n  };\n  do {\n    if (input.charCodeAt(idx) === 33 /*'!'*/) {\n      idx++;\n      ignored();\n      type = {\n        kind: 'NonNullType' as Kind.NON_NULL_TYPE,\n        type: type as ast.NamedTypeNode | ast.ListTypeNode,\n      } satisfies ast.NonNullTypeNode;\n    }\n    if (lists) {\n      if (input.charCodeAt(idx++) !== 93 /*']'*/) throw error('NamedType');\n      ignored();\n      type = {\n        kind: 'ListType' as Kind.LIST_TYPE,\n        type: type as ast.NamedTypeNode | ast.ListTypeNode,\n      } satisfies ast.ListTypeNode;\n    }\n  } while (lists--);\n  return type;\n}\n\n// NOTE: This should be compressed by our build step\n// This merges the two possible selection parsing branches into one regular expression\nconst selectionRe = new RegExp(\n  '(?:' +\n    // fragment spreads (FragmentSpread or InlineFragment nodes)\n    '(\\\\.{3})|' +\n    // field aliases or names (FieldNode)\n    '(' +\n    nameRe.source +\n    '))',\n  'y'\n);\n\n// NOTE: Each of the groups above end up in the RegExpExecArray at the indices 1&2\nconst enum SelectionGroup {\n  Spread = 1,\n  Name,\n}\n\ntype SelectionExec = RegExpExecArray & {\n  [Prop in SelectionGroup]: string | undefined;\n};\n\nfunction selectionSet(): ast.SelectionSetNode {\n  const selections: ast.SelectionNode[] = [];\n  let match: string | undefined;\n  let exec: SelectionExec | null;\n  do {\n    selectionRe.lastIndex = idx;\n    if ((exec = selectionRe.exec(input) as SelectionExec) != null) {\n      idx = selectionRe.lastIndex;\n      if (exec[SelectionGroup.Spread] != null) {\n        ignored();\n        let match = advance(nameRe);\n        if (match != null && match !== 'on') {\n          // A simple `...Name` spread with optional directives\n          ignored();\n          selections.push({\n            kind: 'FragmentSpread' as Kind.FRAGMENT_SPREAD,\n            name: { kind: 'Name' as Kind.NAME, value: match },\n            directives: directives(false),\n          });\n        } else {\n          ignored();\n          if (match === 'on') {\n            // An inline `... on Name` spread; if this doesn't match, the type condition has been omitted\n            if ((match = advance(nameRe)) == null) throw error('NamedType');\n            ignored();\n          }\n          const _directives = directives(false);\n          if (input.charCodeAt(idx++) !== 123 /*'{'*/) throw error('InlineFragment');\n          ignored();\n          selections.push({\n            kind: 'InlineFragment' as Kind.INLINE_FRAGMENT,\n            typeCondition: match\n              ? {\n                  kind: 'NamedType' as Kind.NAMED_TYPE,\n                  name: { kind: 'Name' as Kind.NAME, value: match },\n                }\n              : undefined,\n            directives: _directives,\n            selectionSet: selectionSet(),\n          });\n        }\n      } else if ((match = exec[SelectionGroup.Name]) != null) {\n        let _alias: string | undefined;\n        ignored();\n        // Parse the optional alias, by reassigning and then getting the name\n        if (input.charCodeAt(idx) === 58 /*':'*/) {\n          idx++;\n          ignored();\n          _alias = match;\n          if ((match = advance(nameRe)) == null) throw error('Field');\n          ignored();\n        }\n        const _arguments = arguments_(false);\n        ignored();\n        const _directives = directives(false);\n        let _selectionSet: ast.SelectionSetNode | undefined;\n        if (input.charCodeAt(idx) === 123 /*'{'*/) {\n          idx++;\n          ignored();\n          _selectionSet = selectionSet();\n        }\n        selections.push({\n          kind: 'Field' as Kind.FIELD,\n          alias: _alias ? { kind: 'Name' as Kind.NAME, value: _alias } : undefined,\n          name: { kind: 'Name' as Kind.NAME, value: match },\n          arguments: _arguments,\n          directives: _directives,\n          selectionSet: _selectionSet,\n        });\n      }\n    } else {\n      throw error('SelectionSet');\n    }\n  } while (input.charCodeAt(idx) !== 125 /*'}'*/);\n  idx++;\n  ignored();\n  return {\n    kind: 'SelectionSet' as Kind.SELECTION_SET,\n    selections,\n  };\n}\n\nfunction variableDefinitions(): ast.VariableDefinitionNode[] | undefined {\n  ignored();\n  if (input.charCodeAt(idx) === 40 /*'('*/) {\n    const vars: ast.VariableDefinitionNode[] = [];\n    idx++;\n    ignored();\n    let _name: string | undefined;\n    do {\n      if (input.charCodeAt(idx++) !== 36 /*'$'*/) throw error('Variable');\n      if ((_name = advance(nameRe)) == null) throw error('Variable');\n      ignored();\n      if (input.charCodeAt(idx++) !== 58 /*':'*/) throw error('VariableDefinition');\n      ignored();\n      const _type = type();\n      let _defaultValue: ast.ConstValueNode | undefined;\n      if (input.charCodeAt(idx) === 61 /*'='*/) {\n        idx++;\n        ignored();\n        _defaultValue = value(true);\n      }\n      ignored();\n      vars.push({\n        kind: 'VariableDefinition' as Kind.VARIABLE_DEFINITION,\n        variable: {\n          kind: 'Variable' as Kind.VARIABLE,\n          name: { kind: 'Name' as Kind.NAME, value: _name },\n        },\n        type: _type,\n        defaultValue: _defaultValue,\n        directives: directives(true),\n      });\n    } while (input.charCodeAt(idx) !== 41 /*')'*/);\n    idx++;\n    ignored();\n    return vars;\n  }\n}\n\nfunction fragmentDefinition(): ast.FragmentDefinitionNode {\n  let _name: string | undefined;\n  let _condition: string | undefined;\n  if ((_name = advance(nameRe)) == null) throw error('FragmentDefinition');\n  ignored();\n  if (advance(nameRe) !== 'on') throw error('FragmentDefinition');\n  ignored();\n  if ((_condition = advance(nameRe)) == null) throw error('FragmentDefinition');\n  ignored();\n  const _directives = directives(false);\n  if (input.charCodeAt(idx++) !== 123 /*'{'*/) throw error('FragmentDefinition');\n  ignored();\n  return {\n    kind: 'FragmentDefinition' as Kind.FRAGMENT_DEFINITION,\n    name: { kind: 'Name' as Kind.NAME, value: _name },\n    typeCondition: {\n      kind: 'NamedType' as Kind.NAMED_TYPE,\n      name: { kind: 'Name' as Kind.NAME, value: _condition },\n    },\n    directives: _directives,\n    selectionSet: selectionSet(),\n  };\n}\n\nconst definitionRe = /(?:query|mutation|subscription|fragment)/y;\n\nfunction operationDefinition(\n  operation: OperationTypeNode | undefined\n): ast.OperationDefinitionNode | undefined {\n  let _name: string | undefined;\n  let _variableDefinitions: ast.VariableDefinitionNode[] | undefined;\n  let _directives: ast.DirectiveNode[] | undefined;\n  if (operation) {\n    ignored();\n    _name = advance(nameRe);\n    _variableDefinitions = variableDefinitions();\n    _directives = directives(false);\n  }\n  if (input.charCodeAt(idx) === 123 /*'{'*/) {\n    idx++;\n    ignored();\n    return {\n      kind: 'OperationDefinition' as Kind.OPERATION_DEFINITION,\n      operation: operation || ('query' as OperationTypeNode.QUERY),\n      name: _name ? { kind: 'Name' as Kind.NAME, value: _name } : undefined,\n      variableDefinitions: _variableDefinitions,\n      directives: _directives,\n      selectionSet: selectionSet(),\n    };\n  }\n}\n\nfunction document(input: string, noLoc: boolean): ast.DocumentNode {\n  let match: string | undefined;\n  let definition: ast.OperationDefinitionNode | undefined;\n  ignored();\n  const definitions: ast.ExecutableDefinitionNode[] = [];\n  do {\n    if ((match = advance(definitionRe)) === 'fragment') {\n      ignored();\n      definitions.push(fragmentDefinition());\n    } else if ((definition = operationDefinition(match as OperationTypeNode)) != null) {\n      definitions.push(definition);\n    } else {\n      throw error('Document');\n    }\n  } while (idx < input.length);\n\n  if (!noLoc) {\n    let loc: Location | undefined;\n    return {\n      kind: 'Document' as Kind.DOCUMENT,\n      definitions,\n      /* v8 ignore start */\n      set loc(_loc: Location) {\n        loc = _loc;\n      },\n      /* v8 ignore stop */\n      // @ts-ignore\n      get loc() {\n        if (!loc) {\n          loc = {\n            start: 0,\n            end: input.length,\n            startToken: undefined,\n            endToken: undefined,\n            source: {\n              body: input,\n              name: 'graphql.web',\n              locationOffset: { line: 1, column: 1 },\n            },\n          };\n        }\n        return loc;\n      },\n    };\n  }\n\n  return {\n    kind: 'Document' as Kind.DOCUMENT,\n    definitions,\n  };\n}\n\ntype ParseOptions = {\n  [option: string]: any;\n};\n\nexport function parse(\n  string: string | Source,\n  options?: ParseOptions | undefined\n): ast.DocumentNode {\n  input = typeof string.body === 'string' ? string.body : string;\n  idx = 0;\n  return document(input, options && options.noLocation);\n}\n\nexport function parseValue(\n  string: string | Source,\n  _options?: ParseOptions | undefined\n): ast.ValueNode {\n  input = typeof string.body === 'string' ? string.body : string;\n  idx = 0;\n  ignored();\n  return value(false);\n}\n\nexport function parseType(\n  string: string | Source,\n  _options?: ParseOptions | undefined\n): ast.TypeNode {\n  input = typeof string.body === 'string' ? string.body : string;\n  idx = 0;\n  return type();\n}\n", "import type { ASTNode } from './ast';\n\nexport const BREAK = {};\n\nexport function visit<N extends ASTNode>(root: N, visitor: ASTVisitor): N;\nexport function visit<R>(root: ASTNode, visitor: ASTReducer<R>): R;\n\nexport function visit(node: ASTNode, visitor: ASTVisitor | ASTReducer<any>) {\n  const ancestors: Array<ASTNode | ReadonlyArray<ASTNode>> = [];\n  const path: Array<string | number> = [];\n\n  function traverse(\n    node: ASTNode,\n    key?: string | number | undefined,\n    parent?: ASTNode | ReadonlyArray<ASTNode> | undefined\n  ) {\n    let hasEdited = false;\n\n    const enter =\n      (visitor[node.kind] && visitor[node.kind].enter) ||\n      visitor[node.kind] ||\n      (visitor as EnterLeaveVisitor<ASTNode>).enter;\n    const resultEnter = enter && enter.call(visitor, node, key, parent, path, ancestors);\n    if (resultEnter === false) {\n      return node;\n    } else if (resultEnter === null) {\n      return null;\n    } else if (resultEnter === BREAK) {\n      throw BREAK;\n    } else if (resultEnter && typeof resultEnter.kind === 'string') {\n      hasEdited = resultEnter !== node;\n      node = resultEnter;\n    }\n\n    if (parent) ancestors.push(parent);\n\n    let result: any;\n    const copy = { ...node };\n    for (const nodeKey in node) {\n      path.push(nodeKey);\n      let value = node[nodeKey];\n      if (Array.isArray(value)) {\n        const newValue: any[] = [];\n        for (let index = 0; index < value.length; index++) {\n          if (value[index] != null && typeof value[index].kind === 'string') {\n            ancestors.push(node);\n            path.push(index);\n            result = traverse(value[index], index, value);\n            path.pop();\n            ancestors.pop();\n            if (result == null) {\n              hasEdited = true;\n            } else {\n              hasEdited = hasEdited || result !== value[index];\n              newValue.push(result);\n            }\n          }\n        }\n        value = newValue;\n      } else if (value != null && typeof value.kind === 'string') {\n        result = traverse(value, nodeKey, node);\n        if (result !== undefined) {\n          hasEdited = hasEdited || value !== result;\n          value = result;\n        }\n      }\n\n      path.pop();\n      if (hasEdited) copy[nodeKey] = value;\n    }\n\n    if (parent) ancestors.pop();\n    const leave =\n      (visitor[node.kind] && visitor[node.kind].leave) ||\n      (visitor as EnterLeaveVisitor<ASTNode>).leave;\n    const resultLeave = leave && leave.call(visitor, node, key, parent, path, ancestors);\n    if (resultLeave === BREAK) {\n      throw BREAK;\n    } else if (resultLeave !== undefined) {\n      return resultLeave;\n    } else if (resultEnter !== undefined) {\n      return hasEdited ? copy : resultEnter;\n    } else {\n      return hasEdited ? copy : node;\n    }\n  }\n\n  try {\n    const result = traverse(node);\n    return result !== undefined && result !== false ? result : node;\n  } catch (error) {\n    if (error !== BREAK) throw error;\n    return node;\n  }\n}\n\nexport type ASTVisitor = EnterLeaveVisitor<ASTNode> | KindVisitor;\n\ntype KindVisitor = {\n  readonly [NodeT in ASTNode as NodeT['kind']]?: ASTVisitFn<NodeT> | EnterLeaveVisitor<NodeT>;\n};\n\ninterface EnterLeaveVisitor<TVisitedNode extends ASTNode> {\n  readonly enter?: ASTVisitFn<TVisitedNode> | undefined;\n  readonly leave?: ASTVisitFn<TVisitedNode> | undefined;\n}\n\nexport type ASTVisitFn<Node extends ASTNode> = (\n  node: Node,\n  key: string | number | undefined,\n  parent: ASTNode | ReadonlyArray<ASTNode> | undefined,\n  path: ReadonlyArray<string | number>,\n  ancestors: ReadonlyArray<ASTNode | ReadonlyArray<ASTNode>>\n) => any;\n\nexport type ASTReducer<R> = {\n  readonly [NodeT in ASTNode as NodeT['kind']]?: {\n    readonly enter?: ASTVisitFn<NodeT>;\n    readonly leave: ASTReducerFn<NodeT, R>;\n  };\n};\n\ntype ASTReducerFn<TReducedNode extends ASTNode, R> = (\n  node: { [K in keyof TReducedNode]: ReducedField<TReducedNode[K], R> },\n  key: string | number | undefined,\n  parent: ASTNode | ReadonlyArray<ASTNode> | undefined,\n  path: ReadonlyArray<string | number>,\n  ancestors: ReadonlyArray<ASTNode | ReadonlyArray<ASTNode>>\n) => R;\n\ntype ReducedField<T, R> = T extends null | undefined\n  ? T\n  : T extends ReadonlyArray<any>\n    ? ReadonlyArray<R>\n    : R;\n", "import type {\n  ASTNode,\n  NameNode,\n  DocumentNode,\n  VariableNode,\n  SelectionSetNode,\n  FieldNode,\n  ArgumentNode,\n  FragmentSpreadNode,\n  InlineFragmentNode,\n  VariableDefinitionNode,\n  OperationDefinitionNode,\n  FragmentDefinitionNode,\n  IntValueNode,\n  FloatValueNode,\n  StringValueNode,\n  BooleanValueNode,\n  NullValueNode,\n  EnumValueNode,\n  ListValueNode,\n  ObjectValueNode,\n  ObjectFieldNode,\n  DirectiveNode,\n  NamedTypeNode,\n  ListTypeNode,\n  NonNullTypeNode,\n} from './ast';\n\nfunction mapJoin<T>(value: readonly T[], joiner: string, mapper: (value: T) => string): string {\n  let out = '';\n  for (let index = 0; index < value.length; index++) {\n    if (index) out += joiner;\n    out += mapper(value[index]);\n  }\n  return out;\n}\n\nfunction printString(string: string): string {\n  return JSON.stringify(string);\n}\n\nfunction printBlockString(string: string): string {\n  return '\"\"\"\\n' + string.replace(/\"\"\"/g, '\\\\\"\"\"') + '\\n\"\"\"';\n}\n\nconst MAX_LINE_LENGTH = 80;\n\nlet LF = '\\n';\n\nconst nodes = {\n  OperationDefinition(node: OperationDefinitionNode): string {\n    let out: string = node.operation;\n    if (node.name) out += ' ' + node.name.value;\n    if (node.variableDefinitions && node.variableDefinitions.length) {\n      if (!node.name) out += ' ';\n      out += '(' + mapJoin(node.variableDefinitions, ', ', nodes.VariableDefinition) + ')';\n    }\n    if (node.directives && node.directives.length)\n      out += ' ' + mapJoin(node.directives, ' ', nodes.Directive);\n    return out !== 'query'\n      ? out + ' ' + nodes.SelectionSet(node.selectionSet)\n      : nodes.SelectionSet(node.selectionSet);\n  },\n  VariableDefinition(node: VariableDefinitionNode): string {\n    let out = nodes.Variable!(node.variable) + ': ' + _print(node.type);\n    if (node.defaultValue) out += ' = ' + _print(node.defaultValue);\n    if (node.directives && node.directives.length)\n      out += ' ' + mapJoin(node.directives, ' ', nodes.Directive);\n    return out;\n  },\n  Field(node: FieldNode): string {\n    let out = node.alias ? node.alias.value + ': ' + node.name.value : node.name.value;\n    if (node.arguments && node.arguments.length) {\n      const args = mapJoin(node.arguments, ', ', nodes.Argument);\n      if (out.length + args.length + 2 > MAX_LINE_LENGTH) {\n        out +=\n          '(' +\n          (LF += '  ') +\n          mapJoin(node.arguments, LF, nodes.Argument) +\n          (LF = LF.slice(0, -2)) +\n          ')';\n      } else {\n        out += '(' + args + ')';\n      }\n    }\n    if (node.directives && node.directives.length)\n      out += ' ' + mapJoin(node.directives, ' ', nodes.Directive);\n    if (node.selectionSet && node.selectionSet.selections.length) {\n      out += ' ' + nodes.SelectionSet(node.selectionSet);\n    }\n    return out;\n  },\n  StringValue(node: StringValueNode): string {\n    if (node.block) {\n      return printBlockString(node.value).replace(/\\n/g, LF);\n    } else {\n      return printString(node.value);\n    }\n  },\n  BooleanValue(node: BooleanValueNode): string {\n    return '' + node.value;\n  },\n  NullValue(_node: NullValueNode): string {\n    return 'null';\n  },\n  IntValue(node: IntValueNode): string {\n    return node.value;\n  },\n  FloatValue(node: FloatValueNode): string {\n    return node.value;\n  },\n  EnumValue(node: EnumValueNode): string {\n    return node.value;\n  },\n  Name(node: NameNode): string {\n    return node.value;\n  },\n  Variable(node: VariableNode): string {\n    return '$' + node.name.value;\n  },\n  ListValue(node: ListValueNode): string {\n    return '[' + mapJoin(node.values, ', ', _print) + ']';\n  },\n  ObjectValue(node: ObjectValueNode): string {\n    return '{' + mapJoin(node.fields, ', ', nodes.ObjectField) + '}';\n  },\n  ObjectField(node: ObjectFieldNode): string {\n    return node.name.value + ': ' + _print(node.value);\n  },\n  Document(node: DocumentNode): string {\n    if (!node.definitions || !node.definitions.length) return '';\n    return mapJoin(node.definitions, '\\n\\n', _print);\n  },\n  SelectionSet(node: SelectionSetNode): string {\n    return '{' + (LF += '  ') + mapJoin(node.selections, LF, _print) + (LF = LF.slice(0, -2)) + '}';\n  },\n  Argument(node: ArgumentNode): string {\n    return node.name.value + ': ' + _print(node.value);\n  },\n  FragmentSpread(node: FragmentSpreadNode): string {\n    let out = '...' + node.name.value;\n    if (node.directives && node.directives.length)\n      out += ' ' + mapJoin(node.directives, ' ', nodes.Directive);\n    return out;\n  },\n  InlineFragment(node: InlineFragmentNode): string {\n    let out = '...';\n    if (node.typeCondition) out += ' on ' + node.typeCondition.name.value;\n    if (node.directives && node.directives.length)\n      out += ' ' + mapJoin(node.directives, ' ', nodes.Directive);\n    out += ' ' + nodes.SelectionSet(node.selectionSet);\n    return out;\n  },\n  FragmentDefinition(node: FragmentDefinitionNode): string {\n    let out = 'fragment ' + node.name.value;\n    out += ' on ' + node.typeCondition.name.value;\n    if (node.directives && node.directives.length)\n      out += ' ' + mapJoin(node.directives, ' ', nodes.Directive);\n    return out + ' ' + nodes.SelectionSet(node.selectionSet);\n  },\n  Directive(node: DirectiveNode): string {\n    let out = '@' + node.name.value;\n    if (node.arguments && node.arguments.length)\n      out += '(' + mapJoin(node.arguments, ', ', nodes.Argument) + ')';\n    return out;\n  },\n  NamedType(node: NamedTypeNode): string {\n    return node.name.value;\n  },\n  ListType(node: ListTypeNode): string {\n    return '[' + _print(node.type) + ']';\n  },\n  NonNullType(node: NonNullTypeNode): string {\n    return _print(node.type) + '!';\n  },\n} as const;\n\nconst _print = (node: ASTNode): string => nodes[node.kind](node);\n\nfunction print(node: ASTNode): string {\n  LF = '\\n';\n  return nodes[node.kind] ? nodes[node.kind](node) : '';\n}\n\nexport { print, printString, printBlockString };\n", "import type { TypeNode, ValueNode } from './ast';\nimport type { Maybe } from './types';\n\nexport function valueFromASTUntyped(\n  node: ValueNode,\n  variables?: Maybe<Record<string, any>>\n): unknown {\n  switch (node.kind) {\n    case 'NullValue':\n      return null;\n    case 'IntValue':\n      return parseInt(node.value, 10);\n    case 'FloatValue':\n      return parseFloat(node.value);\n    case 'StringValue':\n    case 'EnumValue':\n    case 'BooleanValue':\n      return node.value;\n    case 'ListValue': {\n      const values: unknown[] = [];\n      for (let i = 0, l = node.values.length; i < l; i++)\n        values.push(valueFromASTUntyped(node.values[i], variables));\n      return values;\n    }\n    case 'ObjectValue': {\n      const obj = Object.create(null);\n      for (let i = 0, l = node.fields.length; i < l; i++) {\n        const field = node.fields[i];\n        obj[field.name.value] = valueFromASTUntyped(field.value, variables);\n      }\n      return obj;\n    }\n    case 'Variable':\n      return variables && variables[node.name.value];\n  }\n}\n\nexport function valueFromTypeNode(\n  node: ValueNode,\n  type: TypeNode,\n  variables?: Maybe<Record<string, any>>\n): unknown {\n  if (node.kind === 'Variable') {\n    const variableName = node.name.value;\n    return variables ? valueFromTypeNode(variables[variableName], type, variables) : undefined;\n  } else if (type.kind === 'NonNullType') {\n    return node.kind !== 'NullValue' ? valueFromTypeNode(node, type, variables) : undefined;\n  } else if (node.kind === 'NullValue') {\n    return null;\n  } else if (type.kind === 'ListType') {\n    if (node.kind === 'ListValue') {\n      const values: unknown[] = [];\n      for (let i = 0, l = node.values.length; i < l; i++) {\n        const value = node.values[i];\n        const coerced = valueFromTypeNode(value, type.type, variables);\n        if (coerced === undefined) {\n          return undefined;\n        } else {\n          values.push(coerced);\n        }\n      }\n      return values;\n    }\n  } else if (type.kind === 'NamedType') {\n    switch (type.name.value) {\n      case 'Int':\n      case 'Float':\n      case 'String':\n      case 'Bool':\n        return type.name.value + 'Value' === node.kind\n          ? valueFromASTUntyped(node, variables)\n          : undefined;\n      default:\n        return valueFromASTUntyped(node, variables);\n    }\n  }\n}\n", "import type { Location, Source as _Source } from './types';\nimport type { ASTNode, SelectionNode } from './ast';\n\nexport function isSelectionNode(node: ASTNode): node is SelectionNode {\n  return node.kind === 'Field' || node.kind === 'FragmentSpread' || node.kind === 'InlineFragment';\n}\n\nexport function Source(body: string, name?: string, locationOffset?: Location): _Source {\n  return {\n    body,\n    name,\n    locationOffset: locationOffset || { line: 1, column: 1 },\n  };\n}\n"], "names": ["Kind", "NAME", "DOCUMENT", "OPERATION_DEFINITION", "VARIABLE_DEFINITION", "SELECTION_SET", "FIELD", "ARGUMENT", "FRAGMENT_SPREAD", "INLINE_FRAGMENT", "FRAGMENT_DEFINITION", "VARIABLE", "INT", "FLOAT", "STRING", "BOOLEAN", "NULL", "ENUM", "LIST", "OBJECT", "OBJECT_FIELD", "DIRECTIVE", "NAMED_TYPE", "LIST_TYPE", "NON_NULL_TYPE", "OperationTypeNode", "QUERY", "MUTATION", "SUBSCRIPTION", "GraphQLError", "Error", "constructor", "message", "nodes", "source", "positions", "path", "originalError", "extensions", "super", "this", "name", "Array", "isArray", "_extensions", "originalExtensions", "toJSON", "toString", "Symbol", "toStringTag", "input", "idx", "error", "kind", "advance", "pattern", "lastIndex", "test", "slice", "leadingRe", "blockString", "string", "lines", "split", "out", "commonIndent", "firstNonEmptyLine", "lastNonEmptyLine", "length", "i", "replace", "ignored", "char", "charCodeAt", "nameRe", "valueRe", "RegExp", "ValueGroup", "complexStringRe", "value", "constant", "match", "exec", "values", "push", "fields", "Const", "Var", "Int", "floatPart", "Float", "BlockString", "block", "String", "JSON", "parse", "Enum", "arguments_", "args", "_name", "directives", "arguments", "type", "lists", "selectionRe", "SelectionGroup", "selectionSet", "selections", "Spread", "_directives", "typeCondition", "undefined", "Name", "_alias", "_arguments", "_selectionSet", "alias", "fragmentDefinition", "_condition", "definitionRe", "operationDefinition", "operation", "_variableDefinitions", "variableDefinitions", "vars", "_type", "_defaultValue", "variable", "defaultValue", "options", "document", "noLoc", "definition", "definitions", "loc", "_loc", "start", "end", "startToken", "endToken", "body", "locationOffset", "line", "column", "noLocation", "parseValue", "_options", "parseType", "BREAK", "visit", "node", "visitor", "ancestors", "result", "traverse", "key", "parent", "hasEdited", "enter", "resultEnter", "call", "copy", "nodeKey", "newValue", "index", "pop", "leave", "resultLeave", "mapJoin", "joiner", "mapper", "printString", "stringify", "printBlockString", "LF", "OperationDefinition", "VariableDefinition", "Directive", "SelectionSet", "Variable", "_print", "Field", "Argument", "StringValue", "BooleanValue", "Null<PERSON><PERSON>ue", "_node", "IntValue", "FloatValue", "EnumValue", "ListValue", "ObjectValue", "ObjectField", "Document", "FragmentSpread", "InlineFragment", "FragmentDefinition", "NamedType", "ListType", "NonNullType", "print", "valueFromASTUntyped", "variables", "parseInt", "parseFloat", "l", "obj", "Object", "create", "field", "valueFromTypeNode", "coerced", "isSelectionNode", "Source"], "mappings": "AAAO,IAAMA,IAAO;EAClBC,MAAM;EACNC,UAAU;EACVC,sBAAsB;EACtBC,qBAAqB;EACrBC,eAAe;EACfC,OAAO;EACPC,UAAU;EACVC,iBAAiB;EACjBC,iBAAiB;EACjBC,qBAAqB;EACrBC,UAAU;EACVC,KAAK;EACLC,OAAO;EACPC,QAAQ;EACRC,SAAS;EACTC,MAAM;EACNC,MAAM;EACNC,MAAM;EACNC,QAAQ;EACRC,cAAc;EACdC,WAAW;EACXC,YAAY;EACZC,WAAW;EACXC,eAAe;;;AAyBV,IAAMC,IAAoB;EAC/BC,OAAO;EACPC,UAAU;EACVC,cAAc;;;ACjDT,MAAMC,qBAAqBC;EAShCC,WAAAA,CACEC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC;IAEAC,MAAMP;IAENQ,KAAKC,OAAO;IACZD,KAAKR,UAAUA;IAEf,IAAII;MAAMI,KAAKJ,OAAOA;;IACtB,IAAIH;MAAOO,KAAKP,QAASS,MAAMC,QAAQV,KAASA,IAAQ,EAACA;;IACzD,IAAIC;MAAQM,KAAKN,SAASA;;IAC1B,IAAIC;MAAWK,KAAKL,YAAYA;;IAChC,IAAIE;MAAeG,KAAKH,gBAAgBA;;IAExC,IAAIO,IAAcN;IAClB,KAAKM,KAAeP,GAAe;MACjC,IAAMQ,IAAsBR,EAAsBC;MAClD,IAAIO,KAAoD,mBAAvBA;QAC/BD,IAAcC;;AAElB;IAEAL,KAAKF,aAAaM,KAAe;AACnC;EAEAE,MAAAA;IACE,OAAO;SAAKN;MAAMR,SAASQ,KAAKR;;AAClC;EAEAe,QAAAA;IACE,OAAOP,KAAKR;AACd;EAEA,KAAKgB,OAAOC;IACV,OAAO;AACT;;;AC1CF,IAAIC;;AACJ,IAAIC;;AAEJ,SAASC,MAAMC;EACb,OAAO,IAAIxB,aAAc,qCAAoCsB,QAAUE;AACzE;;AAEA,SAASC,QAAQC;EACfA,EAAQC,YAAYL;EACpB,IAAII,EAAQE,KAAKP,IAAQ;IAEvB,OADcA,EAAMQ,MAAMP,GAAMA,IAAMI,EAAQC;AAEhD;AACF;;AAEA,IAAMG,IAAY;;AAClB,SAASC,YAAYC;EACnB,IAAMC,IAAQD,EAAOE,MAAM;EAC3B,IAAIC,IAAM;EACV,IAAIC,IAAe;EACnB,IAAIC,IAAoB;EACxB,IAAIC,IAAmBL,EAAMM,SAAS;EACtC,KAAK,IAAIC,IAAI,GAAGA,IAAIP,EAAMM,QAAQC,KAAK;IACrCV,EAAUH,YAAY;IACtB,IAAIG,EAAUF,KAAKK,EAAMO,KAAK;MAC5B,IAAIA,OAAOJ,KAAgBN,EAAUH,YAAYS;QAC/CA,IAAeN,EAAUH;;MAC3BU,IAAoBA,KAAqBG;MACzCF,IAAmBE;AACrB;AACF;EACA,KAAK,IAAIA,IAAIH,GAAmBG,KAAKF,GAAkBE,KAAK;IAC1D,IAAIA,MAAMH;MAAmBF,KAAO;;IACpCA,KAAOF,EAAMO,GAAGX,MAAMO,GAAcK,QAAQ,UAAU;AACxD;EACA,OAAON;AACT;;AAGA,SAASO;EACP,KACE,IAAIC,IAAiC,IAA1BtB,EAAMuB,WAAWtB,MACnB,MAATqB,KACS,OAATA,KACS,OAATA,KACS,OAATA,KACS,OAATA,KACS,OAATA,KACS,UAATA,GACAA,IAAiC,IAA1BtB,EAAMuB,WAAWtB;IAExB,IAAa,OAATqB;MAAqB,OAA4C,QAApCA,IAAOtB,EAAMuB,WAAWtB,SAA2B,OAATqB;;;EAE7ErB;AACF;;AAEA,IAAMuB,IAAS;;AAIf,IAAMC,IAAU,IAAIC,OAClB,8BAKEF,EAAOxC,SALT,wHAeEwC,EAAOxC,SACP,MACF;;AAGF,IACW2C,aAAAA;EAAAA,EAAAA,EAAU,QAAA,KAAA;EAAVA,EAAAA,EAAU,MAAA,KAAA;EAAVA,EAAAA,EAAU,MAAA,KAAA;EAAVA,EAAAA,EAAU,QAAA,KAAA;EAAVA,EAAAA,EAAU,cAAA,KAAA;EAAVA,EAAAA,EAAU,SAAA,KAAA;EAAVA,EAAAA,EAAU,OAAA,KAAA;EAAA,OAAVA;AAAU,EAAVA,KAAU,CAAA;;AAcrB,IAAMC,IAAkB;;AAKxB,SAASC,MAAMC;EACb,IAAIC;EACJ,IAAIC;EACJP,EAAQnB,YAAYL;EACpB,IAA8B,OAA1BD,EAAMuB,WAAWtB,IAAqB;IAExCA;IACAoB;IACA,IAAMY,IAA0B;IAChC,OAAiC,OAA1BjC,EAAMuB,WAAWtB;MAAqBgC,EAAOC,KAAKL,MAAMC;;IAC/D7B;IACAoB;IACA,OAAO;MACLlB,MAAM;MACN8B;;AAEH,SAAM,IAA8B,QAA1BjC,EAAMuB,WAAWtB,IAAsB;IAEhDA;IACAoB;IACA,IAAMc,IAAgC;IACtC,OAAiC,QAA1BnC,EAAMuB,WAAWtB,IAAsB;MAC5C,IAAiC,SAA5B8B,IAAQ3B,QAAQoB;QAAkB,MAAMtB,MAAM;;MACnDmB;MACA,IAAgC,OAA5BrB,EAAMuB,WAAWtB;QAAuB,MAAMC,MAAM;;MACxDmB;MACAc,EAAOD,KAAK;QACV/B,MAAM;QACNZ,MAAM;UAAEY,MAAM;UAAqB0B,OAAOE;;QAC1CF,OAAOA,MAAMC;;AAEjB;IACA7B;IACAoB;IACA,OAAO;MACLlB,MAAM;MACNgC;;AAEJ,SAAO,IAAiD,SAA5CH,IAAOP,EAAQO,KAAKhC,KAA8B;IAE5DC,IAAMwB,EAAQnB;IACde;IACA,IAAwC,SAAnCU,IAAQC,EAAKL,EAAWS;MAC3B,OAAiB,WAAVL,IACH;QAAE5B,MAAM;UACR;QACEA,MAAM;QACN0B,OAAiB,WAAVE;;WAER,IAAsC,SAAjCA,IAAQC,EAAKL,EAAWU;MAClC,IAAIP;QACF,MAAM5B,MAAM;;QAEZ,OAAO;UACLC,MAAM;UACNZ,MAAM;YACJY,MAAM;YACN0B,OAAOE;;;;WAIR,IAAsC,SAAjCA,IAAQC,EAAKL,EAAWW,OAAe;MACjD,IAAIC;MACJ,IAA4C,SAAvCA,IAAYP,EAAKL,EAAWa;QAC/B,OAAO;UACLrC,MAAM;UACN0B,OAAOE,IAAQQ;;;QAGjB,OAAO;UACLpC,MAAM;UACN0B,OAAOE;;;AAGb,WAAO,IAA8C,SAAzCA,IAAQC,EAAKL,EAAWc;MAClC,OAAO;QACLtC,MAAM;QACN0B,OAAOnB,YAAYqB,EAAMvB,MAAM,IAAI;QACnCkC,QAAO;;WAEJ,IAAyC,SAApCX,IAAQC,EAAKL,EAAWgB;MAClC,OAAO;QACLxC,MAAM;QAGN0B,OAAOD,EAAgBrB,KAAKwB,KAAUa,KAAKC,MAAMd,KAAoBA,EAAMvB,MAAM,IAAI;QACrFkC,QAAO;;WAEJ,IAAuC,SAAlCX,IAAQC,EAAKL,EAAWmB;MAClC,OAAO;QACL3C,MAAM;QACN0B,OAAOE;;;AAGb;EAEA,MAAM7B,MAAM;AACd;;AAEA,SAAS6C,WAAWjB;EAClB,IAA8B,OAA1B9B,EAAMuB,WAAWtB,IAAqB;IACxC,IAAM+C,IAA2B;IACjC/C;IACAoB;IACA,IAAI4B;IACJ,GAAG;MACD,IAAiC,SAA5BA,IAAQ7C,QAAQoB;QAAkB,MAAMtB,MAAM;;MACnDmB;MACA,IAAgC,OAA5BrB,EAAMuB,WAAWtB;QAAuB,MAAMC,MAAM;;MACxDmB;MACA2B,EAAKd,KAAK;QACR/B,MAAM;QACNZ,MAAM;UAAEY,MAAM;UAAqB0B,OAAOoB;;QAC1CpB,OAAOA,MAAMC;;AAEhB,aAAkC,OAA1B9B,EAAMuB,WAAWtB;IAC1BA;IACAoB;IACA,OAAO2B;AACT;AACF;;AAKA,SAASE,WAAWpB;EAClB,IAA8B,OAA1B9B,EAAMuB,WAAWtB,IAAqB;IACxC,IAAMiD,IAAkC;IACxC,IAAID;IACJ,GAAG;MACDhD;MACA,IAAiC,SAA5BgD,IAAQ7C,QAAQoB;QAAkB,MAAMtB,MAAM;;MACnDmB;MACA6B,EAAWhB,KAAK;QACd/B,MAAM;QACNZ,MAAM;UAAEY,MAAM;UAAqB0B,OAAOoB;;QAC1CE,WAAWJ,WAAWjB;;AAEzB,aAAkC,OAA1B9B,EAAMuB,WAAWtB;IAC1B,OAAOiD;AACT;AACF;;AAEA,SAASE;EACP,IAAIrB;EACJ,IAAIsB,IAAQ;EACZ,OAAiC,OAA1BrD,EAAMuB,WAAWtB,IAAqB;IAC3CoD;IACApD;IACAoB;AACF;EACA,IAAiC,SAA5BU,IAAQ3B,QAAQoB;IAAkB,MAAMtB,MAAM;;EACnDmB;EACA,IAAI+B,IAAqB;IACvBjD,MAAM;IACNZ,MAAM;MAAEY,MAAM;MAAqB0B,OAAOE;;;EAE5C,GAAG;IACD,IAA8B,OAA1B/B,EAAMuB,WAAWtB,IAAqB;MACxCA;MACAoB;MACA+B,IAAO;QACLjD,MAAM;QACNiD,MAAMA;;AAEV;IACA,IAAIC,GAAO;MACT,IAAgC,OAA5BrD,EAAMuB,WAAWtB;QAAuB,MAAMC,MAAM;;MACxDmB;MACA+B,IAAO;QACLjD,MAAM;QACNiD,MAAMA;;AAEV;AACD,WAAQC;EACT,OAAOD;AACT;;AAIA,IAAME,IAAc,IAAI5B,OACtB,kBAKEF,EAAOxC,SACP,MACF;;AAGF,IACWuE,aAAAA;EAAAA,EAAAA,EAAc,SAAA,KAAA;EAAdA,EAAAA,EAAc,OAAA,KAAA;EAAA,OAAdA;AAAc,EAAdA,KAAc,CAAA;;AASzB,SAASC;EACP,IAAMC,IAAkC;EACxC,IAAI1B;EACJ,IAAIC;EACJ,GAAG;IACDsB,EAAYhD,YAAYL;IACxB,IAAyD,SAApD+B,IAAOsB,EAAYtB,KAAKhC,KAAkC;MAC7DC,IAAMqD,EAAYhD;MAClB,IAAmC,QAA/B0B,EAAKuB,EAAeG,SAAiB;QACvCrC;QACA,IAAIU,IAAQ3B,QAAQoB;QACpB,IAAa,QAATO,KAA2B,SAAVA,GAAgB;UAEnCV;UACAoC,EAAWvB,KAAK;YACd/B,MAAM;YACNZ,MAAM;cAAEY,MAAM;cAAqB0B,OAAOE;;YAC1CmB,YAAYA,YAAW;;AAE3B,eAAO;UACL7B;UACA,IAAc,SAAVU,GAAgB;YAElB,IAAiC,SAA5BA,IAAQ3B,QAAQoB;cAAkB,MAAMtB,MAAM;;YACnDmB;AACF;UACA,IAAMsC,IAAcT,YAAW;UAC/B,IAAgC,QAA5BlD,EAAMuB,WAAWtB;YAAwB,MAAMC,MAAM;;UACzDmB;UACAoC,EAAWvB,KAAK;YACd/B,MAAM;YACNyD,eAAe7B,IACX;cACE5B,MAAM;cACNZ,MAAM;gBAAEY,MAAM;gBAAqB0B,OAAOE;;qBAE5C8B;YACJX,YAAYS;YACZH,cAAcA;;AAElB;AACF,aAAO,IAA2C,SAAtCzB,IAAQC,EAAKuB,EAAeO,QAAgB;QACtD,IAAIC,SAA0B;QAC9B1C;QAEA,IAA8B,OAA1BrB,EAAMuB,WAAWtB,IAAqB;UACxCA;UACAoB;UACA0C,IAAShC;UACT,IAAiC,SAA5BA,IAAQ3B,QAAQoB;YAAkB,MAAMtB,MAAM;;UACnDmB;AACF;QACA,IAAM2C,IAAajB,YAAW;QAC9B1B;QACA,IAAMsC,IAAcT,YAAW;QAC/B,IAAIe,SAA+C;QACnD,IAA8B,QAA1BjE,EAAMuB,WAAWtB,IAAsB;UACzCA;UACAoB;UACA4C,IAAgBT;AAClB;QACAC,EAAWvB,KAAK;UACd/B,MAAM;UACN+D,OAAOH,IAAS;YAAE5D,MAAM;YAAqB0B,OAAOkC;mBAAWF;UAC/DtE,MAAM;YAAEY,MAAM;YAAqB0B,OAAOE;;UAC1CoB,WAAWa;UACXd,YAAYS;UACZH,cAAcS;;AAElB;AACF;MACE,MAAM/D,MAAM;;AAEf,WAAkC,QAA1BF,EAAMuB,WAAWtB;EAC1BA;EACAoB;EACA,OAAO;IACLlB,MAAM;IACNsD;;AAEJ;;AAwCA,SAASU;EACP,IAAIlB;EACJ,IAAImB;EACJ,IAAiC,SAA5BnB,IAAQ7C,QAAQoB;IAAkB,MAAMtB,MAAM;;EACnDmB;EACA,IAAwB,SAApBjB,QAAQoB;IAAkB,MAAMtB,MAAM;;EAC1CmB;EACA,IAAsC,SAAjC+C,IAAahE,QAAQoB;IAAkB,MAAMtB,MAAM;;EACxDmB;EACA,IAAMsC,IAAcT,YAAW;EAC/B,IAAgC,QAA5BlD,EAAMuB,WAAWtB;IAAwB,MAAMC,MAAM;;EACzDmB;EACA,OAAO;IACLlB,MAAM;IACNZ,MAAM;MAAEY,MAAM;MAAqB0B,OAAOoB;;IAC1CW,eAAe;MACbzD,MAAM;MACNZ,MAAM;QAAEY,MAAM;QAAqB0B,OAAOuC;;;IAE5ClB,YAAYS;IACZH,cAAcA;;AAElB;;AAEA,IAAMa,IAAe;;AAErB,SAASC,oBACPC;EAEA,IAAItB;EACJ,IAAIuB;EACJ,IAAIb;EACJ,IAAIY,GAAW;IACblD;IACA4B,IAAQ7C,QAAQoB;IAChBgD,IAzEJ,SAASC;MACPpD;MACA,IAA8B,OAA1BrB,EAAMuB,WAAWtB,IAAqB;QACxC,IAAMyE,IAAqC;QAC3CzE;QACAoB;QACA,IAAI4B;QACJ,GAAG;UACD,IAAgC,OAA5BjD,EAAMuB,WAAWtB;YAAuB,MAAMC,MAAM;;UACxD,IAAiC,SAA5B+C,IAAQ7C,QAAQoB;YAAkB,MAAMtB,MAAM;;UACnDmB;UACA,IAAgC,OAA5BrB,EAAMuB,WAAWtB;YAAuB,MAAMC,MAAM;;UACxDmB;UACA,IAAMsD,IAAQvB;UACd,IAAIwB,SAA6C;UACjD,IAA8B,OAA1B5E,EAAMuB,WAAWtB,IAAqB;YACxCA;YACAoB;YACAuD,IAAgB/C,OAAM;AACxB;UACAR;UACAqD,EAAKxC,KAAK;YACR/B,MAAM;YACN0E,UAAU;cACR1E,MAAM;cACNZ,MAAM;gBAAEY,MAAM;gBAAqB0B,OAAOoB;;;YAE5CG,MAAMuB;YACNG,cAAcF;YACd1B,YAAYA,YAAW;;AAE1B,iBAAkC,OAA1BlD,EAAMuB,WAAWtB;QAC1BA;QACAoB;QACA,OAAOqD;AACT;AACF,KAqC2BD;IACvBd,IAAcT,YAAW;AAC3B;EACA,IAA8B,QAA1BlD,EAAMuB,WAAWtB,IAAsB;IACzCA;IACAoB;IACA,OAAO;MACLlB,MAAM;MACNoE,WAAWA,KAAc;MACzBhF,MAAM0D,IAAQ;QAAE9C,MAAM;QAAqB0B,OAAOoB;eAAUY;MAC5DY,qBAAqBD;MACrBtB,YAAYS;MACZH,cAAcA;;AAElB;AACF;;AA0DO,SAASX,MACdlC,GACAoE;EAGA9E,IAAM;EACN,OA9DF,SAAS+E,SAAShF,GAAeiF;IAC/B,IAAIlD;IACJ,IAAImD;IACJ7D;IACA,IAAM8D,IAA8C;IACpD;MACE,IAAwC,gBAAnCpD,IAAQ3B,QAAQiE,KAA+B;QAClDhD;QACA8D,EAAYjD,KAAKiC;AAClB,aAAM,IAAsE,SAAjEe,IAAaZ,oBAAoBvC;QAC3CoD,EAAYjD,KAAKgD;;QAEjB,MAAMhF,MAAM;;aAEPD,IAAMD,EAAMkB;IAErB,KAAK+D,GAAO;MACV,IAAIG;MACJ,OAAO;QACLjF,MAAM;QACNgF;QAEA,OAAIC,CAAIC;UACND,IAAMC;AACP;QAGD,OAAID;UACF,KAAKA;YACHA,IAAM;cACJE,OAAO;cACPC,KAAKvF,EAAMkB;cACXsE,iBAAY3B;cACZ4B,eAAU5B;cACV7E,QAAQ;gBACN0G,MAAM1F;gBACNT,MAAM;gBACNoG,gBAAgB;kBAAEC,MAAM;kBAAGC,QAAQ;;;;;UAIzC,OAAOT;AACT;;AAEJ;IAEA,OAAO;MACLjF,MAAM;MACNgF;;AAEJ,GAYSH,CAFPhF,IAA+B,mBAAhBW,EAAO+E,OAAoB/E,EAAO+E,OAAO/E,GAEjCoE,KAAWA,EAAQe;AAC5C;;AAEO,SAASC,WACdpF,GACAqF;EAEAhG,IAA+B,mBAAhBW,EAAO+E,OAAoB/E,EAAO+E,OAAO/E;EACxDV,IAAM;EACNoB;EACA,OAAOQ,OAAM;AACf;;AAEO,SAASoE,UACdtF,GACAqF;EAEAhG,IAA+B,mBAAhBW,EAAO+E,OAAoB/E,EAAO+E,OAAO/E;EACxDV,IAAM;EACN,OAAOmD;AACT;;ACrjBa8C,IAAAA,IAAQ,CAAE;;AAKhB,SAASC,MAAMC,GAAeC;EACnC,IAAMC,IAAqD;EAC3D,IAAMpH,IAA+B;EA8ErC;IACE,IAAMqH,IA7ER,SAASC,SACPJ,GACAK,GACAC;MAEA,IAAIC,KAAY;MAEhB,IAAMC,IACHP,EAAQD,EAAKjG,SAASkG,EAAQD,EAAKjG,MAAMyG,SAC1CP,EAAQD,EAAKjG,SACZkG,EAAuCO;MAC1C,IAAMC,IAAcD,KAASA,EAAME,KAAKT,GAASD,GAAMK,GAAKC,GAAQxH,GAAMoH;MAC1E,KAAoB,MAAhBO;QACF,OAAOT;aACF,IAAoB,SAAhBS;QACT,OAAO;aACF,IAAIA,MAAgBX;QACzB,MAAMA;aACD,IAAIW,KAA2C,mBAArBA,EAAY1G,MAAmB;QAC9DwG,IAAYE,MAAgBT;QAC5BA,IAAOS;AACT;MAEA,IAAIH;QAAQJ,EAAUpE,KAAKwE;;MAE3B,IAAIH;MACJ,IAAMQ,IAAO;WAAKX;;MAClB,KAAK,IAAMY,KAAWZ,GAAM;QAC1BlH,EAAKgD,KAAK8E;QACV,IAAInF,IAAQuE,EAAKY;QACjB,IAAIxH,MAAMC,QAAQoC,IAAQ;UACxB,IAAMoF,IAAkB;UACxB,KAAK,IAAIC,IAAQ,GAAGA,IAAQrF,EAAMX,QAAQgG;YACxC,IAAoB,QAAhBrF,EAAMqF,MAA+C,mBAAtBrF,EAAMqF,GAAO/G,MAAmB;cACjEmG,EAAUpE,KAAKkE;cACflH,EAAKgD,KAAKgF;cACVX,IAASC,SAAS3E,EAAMqF,IAAQA,GAAOrF;cACvC3C,EAAKiI;cACLb,EAAUa;cACV,IAAc,QAAVZ;gBACFI,KAAY;qBACP;gBACLA,IAAYA,KAAaJ,MAAW1E,EAAMqF;gBAC1CD,EAAS/E,KAAKqE;AAChB;AACF;;UAEF1E,IAAQoF;AACV,eAAO,IAAa,QAATpF,KAAuC,mBAAfA,EAAM1B;UAEvC,SAAe0D,OADf0C,IAASC,SAAS3E,GAAOmF,GAASZ,KACR;YACxBO,IAAYA,KAAa9E,MAAU0E;YACnC1E,IAAQ0E;AACV;;QAGFrH,EAAKiI;QACL,IAAIR;UAAWI,EAAKC,KAAWnF;;AACjC;MAEA,IAAI6E;QAAQJ,EAAUa;;MACtB,IAAMC,IACHf,EAAQD,EAAKjG,SAASkG,EAAQD,EAAKjG,MAAMiH,SACzCf,EAAuCe;MAC1C,IAAMC,IAAcD,KAASA,EAAMN,KAAKT,GAASD,GAAMK,GAAKC,GAAQxH,GAAMoH;MAC1E,IAAIe,MAAgBnB;QAClB,MAAMA;aACD,SAAoBrC,MAAhBwD;QACT,OAAOA;aACF,SAAoBxD,MAAhBgD;QACT,OAAOF,IAAYI,IAAOF;;QAE1B,OAAOF,IAAYI,IAAOX;;AAE9B,KAGiBI,CAASJ;IACxB,YAAkBvC,MAAX0C,MAAmC,MAAXA,IAAmBA,IAASH;AAC5D,IAAC,OAAOlG;IACP,IAAIA,MAAUgG;MAAO,MAAMhG;;IAC3B,OAAOkG;AACT;AACF;;AClEA,SAASkB,QAAWzF,GAAqB0F,GAAgBC;EACvD,IAAI1G,IAAM;EACV,KAAK,IAAIoG,IAAQ,GAAGA,IAAQrF,EAAMX,QAAQgG,KAAS;IACjD,IAAIA;MAAOpG,KAAOyG;;IAClBzG,KAAO0G,EAAO3F,EAAMqF;AACtB;EACA,OAAOpG;AACT;;AAEA,SAAS2G,YAAY9G;EACnB,OAAOiC,KAAK8E,UAAU/G;AACxB;;AAEA,SAASgH,iBAAiBhH;EACxB,OAAO,UAAUA,EAAOS,QAAQ,QAAQ,WAAW;AACrD;;AAIA,IAAIwG,IAAK;;AAET,IAAM7I,IAAQ;EACZ8I,mBAAAA,CAAoBzB;IAClB,IAAItF,IAAcsF,EAAK7B;IACvB,IAAI6B,EAAK7G;MAAMuB,KAAO,MAAMsF,EAAK7G,KAAKsC;;IACtC,IAAIuE,EAAK3B,uBAAuB2B,EAAK3B,oBAAoBvD,QAAQ;MAC/D,KAAKkF,EAAK7G;QAAMuB,KAAO;;MACvBA,KAAO,MAAMwG,QAAQlB,EAAK3B,qBAAqB,MAAM1F,EAAM+I,sBAAsB;AACnF;IACA,IAAI1B,EAAKlD,cAAckD,EAAKlD,WAAWhC;MACrCJ,KAAO,MAAMwG,QAAQlB,EAAKlD,YAAY,KAAKnE,EAAMgJ;;IACnD,OAAe,YAARjH,IACHA,IAAM,MAAM/B,EAAMiJ,aAAa5B,EAAK5C,gBACpCzE,EAAMiJ,aAAa5B,EAAK5C;AAC7B;EACDsE,kBAAAA,CAAmB1B;IACjB,IAAItF,IAAM/B,EAAMkJ,SAAU7B,EAAKvB,YAAY,OAAOqD,OAAO9B,EAAKhD;IAC9D,IAAIgD,EAAKtB;MAAchE,KAAO,QAAQoH,OAAO9B,EAAKtB;;IAClD,IAAIsB,EAAKlD,cAAckD,EAAKlD,WAAWhC;MACrCJ,KAAO,MAAMwG,QAAQlB,EAAKlD,YAAY,KAAKnE,EAAMgJ;;IACnD,OAAOjH;AACR;EACDqH,KAAAA,CAAM/B;IACJ,IAAItF,IAAMsF,EAAKlC,QAAQkC,EAAKlC,MAAMrC,QAAQ,OAAOuE,EAAK7G,KAAKsC,QAAQuE,EAAK7G,KAAKsC;IAC7E,IAAIuE,EAAKjD,aAAaiD,EAAKjD,UAAUjC,QAAQ;MAC3C,IAAM8B,IAAOsE,QAAQlB,EAAKjD,WAAW,MAAMpE,EAAMqJ;MACjD,IAAItH,EAAII,SAAS8B,EAAK9B,SAAS,IA7Bb;QA8BhBJ,KACE,OACC8G,KAAM,QACPN,QAAQlB,EAAKjD,WAAWyE,GAAI7I,EAAMqJ,aACjCR,IAAKA,EAAGpH,MAAM,IAAI,MACnB;;QAEFM,KAAO,MAAMkC,IAAO;;AAExB;IACA,IAAIoD,EAAKlD,cAAckD,EAAKlD,WAAWhC;MACrCJ,KAAO,MAAMwG,QAAQlB,EAAKlD,YAAY,KAAKnE,EAAMgJ;;IACnD,IAAI3B,EAAK5C,gBAAgB4C,EAAK5C,aAAaC,WAAWvC;MACpDJ,KAAO,MAAM/B,EAAMiJ,aAAa5B,EAAK5C;;IAEvC,OAAO1C;AACR;EACDuH,WAAAA,CAAYjC;IACV,IAAIA,EAAK1D;MACP,OAAOiF,iBAAiBvB,EAAKvE,OAAOT,QAAQ,OAAOwG;;MAEnD,OAAOH,YAAYrB,EAAKvE;;AAE3B;EACDyG,cAAalC,KACJ,KAAKA,EAAKvE;EAEnB0G,WAAUC,KACD;EAETC,UAASrC,KACAA,EAAKvE;EAEd6G,YAAWtC,KACFA,EAAKvE;EAEd8G,WAAUvC,KACDA,EAAKvE;EAEdiC,MAAKsC,KACIA,EAAKvE;EAEdoG,UAAS7B,KACA,MAAMA,EAAK7G,KAAKsC;EAEzB+G,WAAUxC,KACD,MAAMkB,QAAQlB,EAAKnE,QAAQ,MAAMiG,UAAU;EAEpDW,aAAYzC,KACH,MAAMkB,QAAQlB,EAAKjE,QAAQ,MAAMpD,EAAM+J,eAAe;EAE/DA,aAAY1C,KACHA,EAAK7G,KAAKsC,QAAQ,OAAOqG,OAAO9B,EAAKvE;EAE9CkH,QAAAA,CAAS3C;IACP,KAAKA,EAAKjB,gBAAgBiB,EAAKjB,YAAYjE;MAAQ,OAAO;;IAC1D,OAAOoG,QAAQlB,EAAKjB,aAAa,QAAQ+C;AAC1C;EACDF,cAAa5B,KACJ,OAAOwB,KAAM,QAAQN,QAAQlB,EAAK3C,YAAYmE,GAAIM,WAAWN,IAAKA,EAAGpH,MAAM,IAAI,MAAM;EAE9F4H,UAAShC,KACAA,EAAK7G,KAAKsC,QAAQ,OAAOqG,OAAO9B,EAAKvE;EAE9CmH,cAAAA,CAAe5C;IACb,IAAItF,IAAM,QAAQsF,EAAK7G,KAAKsC;IAC5B,IAAIuE,EAAKlD,cAAckD,EAAKlD,WAAWhC;MACrCJ,KAAO,MAAMwG,QAAQlB,EAAKlD,YAAY,KAAKnE,EAAMgJ;;IACnD,OAAOjH;AACR;EACDmI,cAAAA,CAAe7C;IACb,IAAItF,IAAM;IACV,IAAIsF,EAAKxC;MAAe9C,KAAO,SAASsF,EAAKxC,cAAcrE,KAAKsC;;IAChE,IAAIuE,EAAKlD,cAAckD,EAAKlD,WAAWhC;MACrCJ,KAAO,MAAMwG,QAAQlB,EAAKlD,YAAY,KAAKnE,EAAMgJ;;IAEnD,OADAjH,KAAO,MAAM/B,EAAMiJ,aAAa5B,EAAK5C;AAEtC;EACD0F,kBAAAA,CAAmB9C;IACjB,IAAItF,IAAM,cAAcsF,EAAK7G,KAAKsC;IAClCf,KAAO,SAASsF,EAAKxC,cAAcrE,KAAKsC;IACxC,IAAIuE,EAAKlD,cAAckD,EAAKlD,WAAWhC;MACrCJ,KAAO,MAAMwG,QAAQlB,EAAKlD,YAAY,KAAKnE,EAAMgJ;;IACnD,OAAOjH,IAAM,MAAM/B,EAAMiJ,aAAa5B,EAAK5C;AAC5C;EACDuE,SAAAA,CAAU3B;IACR,IAAItF,IAAM,MAAMsF,EAAK7G,KAAKsC;IAC1B,IAAIuE,EAAKjD,aAAaiD,EAAKjD,UAAUjC;MACnCJ,KAAO,MAAMwG,QAAQlB,EAAKjD,WAAW,MAAMpE,EAAMqJ,YAAY;;IAC/D,OAAOtH;AACR;EACDqI,WAAU/C,KACDA,EAAK7G,KAAKsC;EAEnBuH,UAAShD,KACA,MAAM8B,OAAO9B,EAAKhD,QAAQ;EAEnCiG,aAAYjD,KACH8B,OAAO9B,EAAKhD,QAAQ;;;AAI/B,IAAM8E,SAAU9B,KAA0BrH,EAAMqH,EAAKjG,MAAMiG;;AAE3D,SAASkD,MAAMlD;EACbwB,IAAK;EACL,OAAO7I,EAAMqH,EAAKjG,QAAQpB,EAAMqH,EAAKjG,MAAMiG,KAAQ;AACrD;;ACnLO,SAASmD,oBACdnD,GACAoD;EAEA,QAAQpD,EAAKjG;GACX,KAAK;IACH,OAAO;;GACT,KAAK;IACH,OAAOsJ,SAASrD,EAAKvE,OAAO;;GAC9B,KAAK;IACH,OAAO6H,WAAWtD,EAAKvE;;GACzB,KAAK;GACL,KAAK;GACL,KAAK;IACH,OAAOuE,EAAKvE;;GACd,KAAK;IACH,IAAMI,IAAoB;IAC1B,KAAK,IAAId,IAAI,GAAGwI,IAAIvD,EAAKnE,OAAOf,QAAQC,IAAIwI,GAAGxI;MAC7Cc,EAAOC,KAAKqH,oBAAoBnD,EAAKnE,OAAOd,IAAIqI;;IAClD,OAAOvH;;GAET,KAAK;IACH,IAAM2H,IAAMC,OAAOC,OAAO;IAC1B,KAAK,IAAI3I,IAAI,GAAGwI,IAAIvD,EAAKjE,OAAOjB,QAAQC,IAAIwI,GAAGxI,KAAK;MAClD,IAAM4I,IAAQ3D,EAAKjE,OAAOhB;MAC1ByI,EAAIG,EAAMxK,KAAKsC,SAAS0H,oBAAoBQ,EAAMlI,OAAO2H;AAC3D;IACA,OAAOI;;GAET,KAAK;IACH,OAAOJ,KAAaA,EAAUpD,EAAK7G,KAAKsC;;AAE9C;;AAEO,SAASmI,kBACd5D,GACAhD,GACAoG;EAEA,IAAkB,eAAdpD,EAAKjG,MAAqB;IAE5B,OAAOqJ,IAAYQ,kBAAkBR,EADhBpD,EAAK7G,KAAKsC,QAC+BuB,GAAMoG,UAAa3F;AACnF,SAAO,IAAkB,kBAAdT,EAAKjD;IACd,OAAqB,gBAAdiG,EAAKjG,OAAuB6J,kBAAkB5D,GAAMhD,GAAMoG,UAAa3F;SACzE,IAAkB,gBAAduC,EAAKjG;IACd,OAAO;SACF,IAAkB,eAAdiD,EAAKjD;IACd,IAAkB,gBAAdiG,EAAKjG,MAAsB;MAC7B,IAAM8B,IAAoB;MAC1B,KAAK,IAAId,IAAI,GAAGwI,IAAIvD,EAAKnE,OAAOf,QAAQC,IAAIwI,GAAGxI,KAAK;QAElD,IAAM8I,IAAUD,kBADF5D,EAAKnE,OAAOd,IACeiC,EAAKA,MAAMoG;QACpD,SAAgB3F,MAAZoG;UACF;;UAEAhI,EAAOC,KAAK+H;;AAEhB;MACA,OAAOhI;AACT;SACK,IAAkB,gBAAdmB,EAAKjD;IACd,QAAQiD,EAAK7D,KAAKsC;KAChB,KAAK;KACL,KAAK;KACL,KAAK;KACL,KAAK;MACH,OAAOuB,EAAK7D,KAAKsC,QAAQ,YAAYuE,EAAKjG,OACtCoJ,oBAAoBnD,GAAMoD,UAC1B3F;;KACN;MACE,OAAO0F,oBAAoBnD,GAAMoD;;;AAGzC;;ACzEO,SAASU,gBAAgB9D;EAC9B,OAAqB,YAAdA,EAAKjG,QAAkC,qBAAdiG,EAAKjG,QAA2C,qBAAdiG,EAAKjG;AACzE;;AAEO,SAASgK,OAAOzE,GAAcnG,GAAeoG;EAClD,OAAO;IACLD;IACAnG;IACAoG,gBAAgBA,KAAkB;MAAEC,MAAM;MAAGC,QAAQ;;;AAEzD;;"}