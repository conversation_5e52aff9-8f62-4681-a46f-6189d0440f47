import React from "react";
import { ActivityIndicator, MD2Colors } from "react-native-paper";
import { View, Image } from "react-native";

export default function AppLoading() {
  return (
    <View
      style={{
        flex: 1,
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        padding: 16,
        top: 16,
      }}
    >
      <Image
        style={{
          width: "100%",
          resizeMode: "contain",
          height: 80,
        }}
        source={require("../../assets/images/icon.png")}
      />
      <ActivityIndicator
        animating={true}
        size={"large"}
        color={MD2Colors.orange600}
      />
    </View>
  );
}
