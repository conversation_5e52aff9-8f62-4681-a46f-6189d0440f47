import { RealtimeChannel } from "@supabase/supabase-js";
import { BolInfo } from "../models/bolInfo.model";
import { ShipmentRoute, ShipmentStatusType } from "../models/shipment.model";
import { Messaging, TrackPoint, Tracking } from "../models/tracking.model";
import {
  combineLists,
  getFromCache,
  getId,
  supabase,
  tryPromise,
} from "@utils/app.settings";
import { TrackingService } from "./tracking.service";
import * as expLocation from "expo-location";
import * as Device from "expo-device";
import * as Sentry from "@sentry/react-native";
import { storeInCache } from "@utils/app.settings";
import { StorageKeys } from "../models/common";
import { LoadStatusType, MarketLoad } from "../models/load-market.model";
import * as ImagePicker from "expo-image-picker";

export class ShipmentService {
  subscribe(id: string, clbk: (payload: MarketLoad) => void) {
    const channel = `driver_${id}`;
    return supabase.channel(channel).on(
      "postgres_changes",
      {
        event: "UPDATE",
        schema: "public",
        table: "market_loads",
        filter: `id=eq.${id}`,
      },
      async (payload) => {
        const data = payload.new as MarketLoad;
        const res = await ShipmentService.getLoad(data.id);
        clbk(res.data ?? data);
      }
    );
  }

  subscribeMessage(id: string, clbk: (payload: Messaging) => void) {
    const channel = `driver_message_${id}`;
    return supabase.channel(channel).on(
      "postgres_changes",
      {
        event: "*",
        schema: "public",
        table: "messagings",
        filter: `load=eq.${id}`,
      },
      async (payload) => {
        const data = payload.new as Messaging;
        clbk(data);
      }
    );
  }

  unsubscribe(channel: RealtimeChannel) {
    return supabase.removeChannel(channel);
  }

  static async getLoad(
    id: string
  ): Promise<{ data?: MarketLoad; error?: string }> {
    const { data, error } = await supabase
      .from("market_loads")
      .select("*, carrier(id, name), shipment!inner(*, client(id, name))")
      .eq("id", id)
      .limit(1)
      .single<MarketLoad>();

    if (error) {
      Sentry.captureException(error);
      return { error: "unable to get load" };
    }
    if (!data) {
      await storeInCache(StorageKeys.LOAD, data);
    }
    return { data };
  }

  async get(
    phone: string
  ): Promise<{ data?: MarketLoad; tracking?: Tracking; error?: string }> {
    const { data, error } = await supabase
      .from("market_loads")
      .select("*, carrier(id, name), shipment!inner(*, client(id, name))")
      .like("driver->>phone", phone)
      .in("status", [
        "ASSIGNED",
        "CHECKED_IN",
        "IN_TRANSIT",
        "REVIEW",
        "DELIVERED",
      ])
      .limit(1)
      .order("createdAt", { ascending: false })
      .single<MarketLoad>();

    if (error) {
      Sentry.captureException(error);
      return { error: "unable to get load" };
    }

    if (data) {
      const tracking = await TrackingService.get(data.id);
      return { data, tracking: tracking.data };
    }
    return { error: `No data for ${phone}` };
  }

  async updateOne(load: MarketLoad) {
    const { id: shipmentId, ...shipment } = load.shipment;
    await supabase
      .from("shipments")
      .update({
        ...shipment,
        client: getId(shipment.client),
        broker: getId(shipment.broker),
        updatedName: load.driver.name,
        updatedAt: new Date().toISOString(),
      })
      .eq("id", shipmentId)
      .select();

    const { id, ...payload } = load;
    const res = await supabase
      .from("market_loads")
      .update({
        ...payload,
        broker: getId(payload.broker),
        shipment: getId(payload.shipment),
        carrier: getId(payload.carrier),
        updatedName: load.driver.name,
        updatedAt: new Date().toISOString(),
      })
      .eq("id", id)
      .select("*, carrier(id, name), shipment(*, client(id, name))");
    if (res.error) {
      Sentry.captureException(res.error?.message);
    }
    return (res.data?.at(0) || load) as MarketLoad;
  }

  async getLocation() {
    return await expLocation
      .getCurrentPositionAsync({
        accuracy: expLocation.Accuracy.BestForNavigation,
      })
      .then((location) => {
        return {
          lat: location.coords.latitude,
          lng: location.coords.longitude,
        };
      });
  }

  getDeviceInfo() {
    return `${
      Device.modelName + (Device.modelId ? ":" + Device.modelId : "")
    }|${Device.osVersion}`;
  }

  async trace(
    note: string,
    load: MarketLoad,
    tracking: Tracking,
    type?: "PICKUP" | "DELIVERY"
  ) {
    const location = await this.getLocation();
    const pins = (await getFromCache<TrackPoint[]>(StorageKeys.TRACKS)) || [];

    const tracks = combineLists(
      [
        ...(tracking?.driverTracks ?? []),
        {
          latitude: location.lat,
          longitude: location.lng,
          date: new Date().toISOString(),
        } as TrackPoint,
      ],
      pins
    );

    const payload: Tracking = {
      ...tracking,
      latestLocation: location,
      driverTracks: tracks,
      activities: [
        ...(tracking.activities ?? []),
        {
          status: load.status,
          date: new Date().toISOString(),
          description: note,
          title: "Driver Update",
          userName: load.driver.name,
          device: this.getDeviceInfo(),
          isFromMobile: true,
          location: !type
            ? undefined
            : { type, lat: location.lat, lng: location.lng },
        },
      ],
    };
    const [error, res] = await tryPromise(
      TrackingService.updateTracking(payload, load.driver.name)
    );
    if (error) {
      Sentry.captureException(error, {
        extra: {
          message: "Unable to get driver location",
          loadId: load?.id,
          proId: load?.shipment.proId,
          tracking: tracking?.id,
          driver: load?.driver?.name,
        },
      });
      return;
    }
    return res.data || payload;
  }

  async acceptShipment(load: MarketLoad, tracking: Tracking) {
    const updatedRoutes = load.shipment.routes.map((x) => {
      x.status = "PENDING";
      x.lastTrackingChangeDate = new Date().toISOString();
      return x;
    });

    const uploadLoad = {
      ...load,
      status: LoadStatusType.CHECKED_IN,
      shipment: {
        ...load.shipment,
        status: ShipmentStatusType.CHECKED_IN,
        routes: updatedRoutes,
      },
    };

    const [loadRes, traceRes] = await Promise.all([
      await this.updateOne(uploadLoad),
      await this.trace(
        "Shipment Accepted. Driver Accepted the shipment request.",
        uploadLoad,
        tracking
      ),
    ]);
    return {
      load: (loadRes || uploadLoad) as MarketLoad,
      tracking: (traceRes || tracking) as Tracking,
    };
  }

  async pickupShipment(item: {
    endpoint: ShipmentRoute;
    load: MarketLoad;
    tracking: Tracking;
    bolDetails: BolInfo;
  }) {
    const updatedRoutes = item.load.shipment.routes.map((x) => {
      x.shipper = {
        ...x.shipper,
        lastTrackingChangeDate: new Date().toISOString(),
        status: "ACCEPTED",
      };
      x.status = "PENDING";
      x.lastTrackingChangeDate = new Date().toISOString();
      return x;
    });

    const uploadLoad = {
      ...item.load,
      status: LoadStatusType.IN_TRANSIT,
      shipment: {
        ...item.load.shipment,
        status: ShipmentStatusType.IN_TRANSIT,
        routes: updatedRoutes,
      },
    };

    const notes = `BOL Shipper: ${item.bolDetails.bolName}, BOL #: ${item.bolDetails.bolNumber}, for ${item.bolDetails.quantity} of ${item.bolDetails.bolItemType}.
    comments: ${item.bolDetails.notes}`;
    const [loadRes, traceRes, _] = await Promise.all([
      await this.updateOne(uploadLoad),
      await this.trace(
        `Driver arrived to Shipper and picked up shipment. Shipment picked up from ${item.endpoint.shipper.name} at ${item.endpoint.shipper.address.fullAddress}. | ${notes}`,
        uploadLoad,
        item.tracking,
        "PICKUP"
      ).then(
        async (res) =>
          await this.trace(
            `Shipment is on its way. Live delivery tracking.`,
            uploadLoad,
            {
              ...(res ?? item.tracking)!,
            }
          )
      ),
      await TrackingService.addDocuments(uploadLoad, item.bolDetails.pictures),
    ]);
    return {
      load: (loadRes || uploadLoad) as MarketLoad,
      tracking: (traceRes || item.tracking) as Tracking,
    };
  }

  async dropOffShipment(item: {
    endpoint: ShipmentRoute;
    load: MarketLoad;
    tracking: Tracking;
    bolDetails: BolInfo;
  }) {
    const updatedRoutes = item.load.shipment.routes.map((x) => {
      x.receiver = {
        ...x.receiver,
        lastTrackingChangeDate: new Date().toISOString(),
        status: "ACCEPTED",
      };
      x.status = "COMPLETED";
      x.lastTrackingChangeDate = new Date().toISOString();
      return x;
    });

    const uploadLoad = {
      ...item.load,
      status: LoadStatusType.REVIEW,
      shipment: {
        ...item.load.shipment,
        status: ShipmentStatusType.DELIVERED,
        routes: updatedRoutes,
      },
    };
    const notes = `BOL Receiver: ${item.bolDetails.bolName}, BOL #: ${item.bolDetails.bolNumber}, for ${item.bolDetails.quantity} of ${item.bolDetails.bolItemType}.
    comments: ${item.bolDetails.notes}`;

    const [loadRes, traceRes, _] = await Promise.all([
      await this.updateOne(uploadLoad),
      await this.trace(
        `Driver arrived to Receiver and dropped-off shipment. To ${item.endpoint.receiver.name} at ${item.endpoint.receiver.address.fullAddress}. | ${notes}`,
        uploadLoad,
        item.tracking,
        "DELIVERY"
      ),
      await TrackingService.addDocuments(uploadLoad, item.bolDetails.pictures),
    ]);
    return {
      load: (loadRes || uploadLoad) as MarketLoad,
      tracking: (traceRes || item.tracking) as Tracking,
    };
  }

  async additional(
    load: MarketLoad,
    tracking: Tracking,
    item: {
      notes: string;
      pictures: ImagePicker.ImagePickerAsset[];
    }
  ) {
    await Promise.all([
      await this.trace(
        `Delivered additional documents, reason: ${item.notes}`,
        load,
        tracking
      ),
      await TrackingService.addDocuments(
        load,
        item.pictures,
        `Additional documents of delivery. Reason: ${item.notes}`
      ),
    ]);
    return LoadStatusType.REVIEW;
  }

  getId(value: any) {
    return typeof value === "object"
      ? Object.keys(value).some((key) => key === "id")
        ? value.id
        : value
      : value;
  }

  static getNextStatus = (status: LoadStatusType): LoadStatusType => {
    switch (status) {
      case LoadStatusType.ASSIGNED:
        return LoadStatusType.CHECKED_IN;

      case LoadStatusType.CHECKED_IN:
        return LoadStatusType.IN_TRANSIT;

      case LoadStatusType.IN_TRANSIT:
        return LoadStatusType.DELIVERED;

      default:
        return LoadStatusType.DELIVERED;
    }
  };
}
