{"name": "owe-mobile-app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "build:dev": "npx eas build --profile development --platform android", "doctor": "npx expo-doctor"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@sentry/react-native": "^6.3.0", "@supabase/supabase-js": "^2.48.1", "expo": "~52.0.28", "expo-blur": "~14.0.3", "expo-build-properties": "~0.13.2", "expo-constants": "~17.0.5", "expo-dev-client": "~5.0.11", "expo-device": "~7.0.2", "expo-font": "~13.0.3", "expo-haptics": "~14.0.1", "expo-image-picker": "~16.0.5", "expo-linking": "~7.0.5", "expo-location": "~18.0.5", "expo-router": "~4.0.17", "expo-splash-screen": "~0.29.21", "expo-sqlite": "~15.1.1", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.1", "expo-system-ui": "~4.0.7", "expo-task-manager": "~12.0.5", "expo-updates": "~0.26.15", "expo-web-browser": "~14.0.2", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.6", "react-native-gesture-handler": "~2.20.2", "react-native-gifted-chat": "^2.6.5", "react-native-paper": "^5.13.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "expo-notifications": "~0.29.13"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^15.1.3", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.3", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}