import {
  FlatList,
  StyleSheet,
  Image,
  Dimensions,
  Pressable,
  View,
} from "react-native";
import { Button, Dialog, Portal, Text } from "react-native-paper";
import * as ImagePicker from "expo-image-picker";
import { useState } from "react";
import { FontAwesome5 } from "@expo/vector-icons";

export default function ImageListView({
  files,
  show,
  close,
}: {
  files: ImagePicker.ImagePickerAsset[];
  show: boolean;
  close: (pictures: ImagePicker.ImagePickerAsset[]) => void;
}) {
  const [pictures, setPictures] = useState(files);
  const Item = ({
    item,
    remove,
  }: {
    item: ImagePicker.ImagePickerAsset;
    remove: (picture: ImagePicker.ImagePickerAsset) => void;
  }) => {
    const blurhash =
      "|rF?hV%2WCj[ayj[a|j[az_NaeWBj@ayfRayfQfQM{M|azj[azf6fQfQfQIpWXofj[ayj[j[fQayWCoeoeaya}j[ayfQa{oLj?j[WVj[ayayj[fQoff7azayj[ayj[j[ayofayayayj[fQj[ayayj[ayfjj[j[ayjuayj[";
    return (
      <View
        style={{
          display: "flex",
          gap: 4,
          flexDirection: "row",
          alignItems: "center",
        }}
      >
        <Image source={{ uri: item.uri ?? blurhash }} style={styles.image} />
        <Pressable onPress={() => remove(item)}>
          {() => <FontAwesome5 name="trash-alt" size={24} color="red" />}
        </Pressable>
      </View>
    );
  };

  const removeItem = (picture: ImagePicker.ImagePickerAsset) => {
    setPictures(pictures.filter((item) => item.uri !== picture.uri));
  };

  return (
    <Portal>
      <Dialog visible={show} onDismiss={() => close(pictures)}>
        <Dialog.Title>
          <Text>Picture(s) of Items Uploaded</Text>
        </Dialog.Title>
        <Dialog.Content>
          <View style={{ maxHeight: Dimensions.get("window").height / 2 }}>
            <FlatList
              data={pictures}
              renderItem={({ item }) => (
                <Item item={item} remove={removeItem} />
              )}
              keyExtractor={(item) => item.uri}
            />
          </View>
        </Dialog.Content>
        <Dialog.Actions>
          <Button
            mode="contained"
            style={{ paddingHorizontal: 16 }}
            onPress={() => close(pictures)}
          >
            Ok
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );
}
const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 50,
  },
  modalView: {
    margin: 20,
    backgroundColor: "white",
    borderRadius: 20,
    padding: 35,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  image_container: {
    flex: 1,
    backgroundColor: "#fff",
    alignItems: "center",
    justifyContent: "center",
  },
  item: {
    marginVertical: 16,
    marginHorizontal: 16,
  },
  title: {
    fontSize: 18,
  },
  image: {
    flex: 1,
    width: Dimensions.get("screen").width - 32,
    height: 224,
    backgroundColor: "#0553",
    marginVertical: 8,
  },
});
