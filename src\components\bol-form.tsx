import React, { useEffect, useState } from "react";
import { View } from "react-native";
import {
  ActivityIndicator,
  Button,
  Dialog,
  MD2Colors,
  Portal,
  SegmentedButtons,
  Text,
  TextInput,
} from "react-native-paper";
import * as ImagePicker from "expo-image-picker";
import { Pressable } from "react-native";
import { FontAwesome } from "@expo/vector-icons";
import ImageListView from "./image-view";
import { BolInfo } from "@core/models/bolInfo.model";
import { useCamera } from "./useCamera";

export default function BOLFormView({
  label,
  show,
  dropOffFlow,
  close,
  save,
}: {
  label: string;
  dropOffFlow: boolean;
  show: boolean;
  save: (data: BolInfo) => void;
  close: () => void;
}) {
  const [loading, setLoading] = useState(false);
  const [viewImage, setViewImage] = useState(false);
  const [pictures, setPictures] = useState<ImagePicker.ImagePickerAsset[]>([]);
  const [error, setError] = useState({
    bolNumberError: false,
    bolItemTypeError: false,
    quantityError: false,
    bolNameError: false,
    picturesError: false,
  });
  const { takePhoto } = useCamera();
  const [permission, requestPermission] = ImagePicker.useCameraPermissions();
  const [form, setForm] = useState<BolInfo>({
    id: dropOffFlow ? 3 : 2,
    bolNumber: "",
    bolItemType: "",
    quantity: "",
    bolName: "",
    notes: "",
    pictures: [],
  });

  useEffect(() => {
    if (permission?.status !== ImagePicker.PermissionStatus.GRANTED) {
      requestPermission().then(() => {
        return null;
      });
    }
    return () => {
      setForm({
        id: dropOffFlow ? 3 : 2,
        bolNumber: "",
        bolItemType: "",
        quantity: "",
        bolName: "",
        notes: "",
        pictures: [],
      });
      setError({
        bolNumberError: false,
        bolItemTypeError: false,
        quantityError: false,
        bolNameError: false,
        picturesError: false,
      });
    };
  }, []);

  const onTakePhoto = async () => {
    const res = await takePhoto();
    const images = [...pictures, ...res];
    setPictures(images);
    setError({
      ...error,
      picturesError:
        images.length <= 0
          ? true
          : dropOffFlow
          ? images.length > 1
          : images.length === 1 || images.length > 2,
    });
  };

  const onSubmit = () => {
    const hasErrors = dropOffFlow
      ? !form.bolName || error.bolNameError || error.picturesError
      : !form.bolNumber ||
        error.bolNumberError ||
        !form.bolItemType ||
        error.bolItemTypeError ||
        !form.quantity ||
        error.quantityError ||
        error.picturesError;

    if (hasErrors) {
      if (dropOffFlow) {
        setError({
          ...error,
          bolNameError: !form.bolName || error.bolNameError,
        });
      } else {
        setError({
          ...error,
          bolNameError: !form.bolName || error.bolNameError,
          bolNumberError: !form.bolNumber || error.bolNumberError,
          bolItemTypeError: !form.bolItemType || error.bolItemTypeError,
          quantityError: !form.quantity || error.quantityError,
        });
      }
      setError({ ...error, picturesError: !pictures.length });
      return;
    }

    if (!pictures.length) {
      setError({ ...error, picturesError: true });
      return;
    }
    save({
      ...form,
      pictures,
    });
  };

  const link = () => {
    return <Text>View({pictures.length})</Text>;
  };

  const updatePictures = (pictures: ImagePicker.ImagePickerAsset[]) => {
    setError({ ...error, picturesError: false });
    setViewImage(false);
    if (dropOffFlow) {
      setPictures(pictures.slice(0));
    } else {
      setPictures(pictures);
    }
    setViewImage(false);
  };

  return (
    <Portal>
      <Dialog visible={show} onDismiss={() => close()}>
        <Dialog.Title>
          <View style={{ borderBottomWidth: 1, width: "100%" }}>
            <Text style={{ fontSize: 18 }}>
              {dropOffFlow ? "Drop-off" : "Pick-up"} Details from
            </Text>
            <Text style={{ fontWeight: "bold", fontSize: 20 }}>{label}</Text>
          </View>
        </Dialog.Title>
        <Dialog.Content>
          {!dropOffFlow && (
            <View style={{ width: "100%" }}>
              <TextInput
                label="Freight BOL#"
                returnKeyType="next"
                mode="outlined"
                onChangeText={(text) => {
                  const value = text;
                  const isValid = value.length > 2;
                  setError({ ...error, bolNumberError: !isValid });
                  if (isValid) {
                    setForm({ ...form, bolNumber: value });
                  }
                }}
              />
              {error.bolNumberError && (
                <Text style={{ color: "red" }}>
                  Please enter Freight BOL number.
                </Text>
              )}
            </View>
          )}

          {dropOffFlow && (
            <View style={{ width: "100%" }}>
              <TextInput
                label={dropOffFlow ? "POD Name" : "BOL Name"}
                returnKeyType="next"
                mode="outlined"
                onChangeText={(text) => {
                  const value = text;
                  const isValid = value.length > 2;
                  setError({ ...error, bolNameError: !isValid });
                  if (isValid) {
                    setForm({ ...form, bolName: value });
                  }
                }}
              />
              {error.bolNameError && (
                <Text style={{ color: "red" }}>This field is required.</Text>
              )}
            </View>
          )}

          {!dropOffFlow && (
            <>
              <View style={{ width: "100%", marginTop: 18 }}>
                <Text style={{ fontWeight: "700" }}>Pick Freight Type</Text>
                <SegmentedButtons
                  value={form.bolItemType}
                  onValueChange={(newValue) => {
                    setForm({ ...form, bolItemType: newValue });
                  }}
                  style={{ marginVertical: 10 }}
                  buttons={[
                    {
                      value: "Piece",
                      label: "Piece",
                      showSelectedCheck: true,
                      checkedColor: "orange",
                      style: { backgroundColor: "white" },
                    },
                    {
                      value: "Skid",
                      label: "Skid",
                      showSelectedCheck: true,
                      checkedColor: "orange",
                      style: { backgroundColor: "white" },
                    },
                    {
                      value: "Pallet",
                      label: "Pallet",
                      showSelectedCheck: true,
                      checkedColor: "orange",
                      style: { backgroundColor: "white" },
                    },
                    {
                      value: "Box",
                      label: "Box",
                      showSelectedCheck: true,
                      checkedColor: "orange",
                      style: { backgroundColor: "white" },
                    },
                  ]}
                />
                {error.bolItemTypeError && (
                  <Text style={{ color: "red" }}>This field is required.</Text>
                )}
              </View>
              <View style={{ width: "100%" }}>
                <TextInput
                  label="Quantity"
                  keyboardType="numeric"
                  maxLength={6}
                  returnKeyType="next"
                  onChangeText={(text) => {
                    const value = text;
                    const isNumeric = /[^0-9]/g.test(value);
                    const isValid = value.length > 0 && !isNumeric;
                    setError({ ...error, quantityError: !isValid });
                    if (isValid) {
                      setForm({ ...form, quantity: value });
                    }
                  }}
                  error={error.quantityError}
                  mode="outlined"
                  style={{ marginBottom: error ? 1 : 16 }}
                />
                {error.quantityError && (
                  <Text style={{ color: "red" }}>
                    Quantity is required and greater than 0.
                  </Text>
                )}
              </View>
            </>
          )}

          <View style={{ width: "100%", marginTop: 20 }}>
            <Text style={{ fontWeight: "700" }}>
              {dropOffFlow ? "Take Proof Picture" : "BOL & Freight Pictures"}
            </Text>
            <View
              style={{
                width: "100%",
                display: "flex",
                flexDirection: "row",
                gap: 4,
                justifyContent: "space-between",
                marginVertical: 16,
              }}
            >
              <Pressable onPress={onTakePhoto}>
                {({ pressed }) => (
                  <FontAwesome
                    name="camera"
                    size={25}
                    style={{ marginRight: 15, opacity: pressed ? 0.5 : 1 }}
                  />
                )}
              </Pressable>

              <Pressable onPress={() => setViewImage(true)}>
                {() => (pictures.length ? link() : null)}
              </Pressable>
            </View>
            {viewImage && pictures.length && (
              <ImageListView
                show={viewImage}
                files={pictures}
                close={updatePictures}
              />
            )}
            {error.picturesError && (
              <Text style={{ color: "red" }}>
                {dropOffFlow
                  ? "1 picture is required."
                  : "Pictures are required. " +
                    (pictures.length <= 0
                      ? " 2 pictures missing."
                      : " 1 picture missing.")}
              </Text>
            )}
          </View>
          <View style={{ width: "100%" }}>
            <TextInput
              label="Notes"
              mode="outlined"
              returnKeyType="done"
              onChangeText={(text) => {
                setForm({ ...form, notes: text });
              }}
            />
          </View>
          {Object.values(error).some((value) => !!value) && (
            <Text style={{ color: "red" }}>
              Please complete all required fields to proceed.
            </Text>
          )}
        </Dialog.Content>
        <Dialog.Actions>
          <Button onPress={() => close()}>Cancel</Button>
          <Button
            mode="contained"
            icon={
              loading
                ? () => (
                    <ActivityIndicator
                      animating={true}
                      color={MD2Colors.red800}
                      style={{ marginRight: 4 }}
                    />
                  )
                : undefined
            }
            style={{ paddingHorizontal: 16 }}
            onPress={() => onSubmit()}
          >
            Submit
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );
}
