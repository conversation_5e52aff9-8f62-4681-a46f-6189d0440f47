/* eslint-disable no-mixed-operators */
/* eslint-disable no-unused-vars */
/* eslint-disable no-bitwise */
/**
 * Primary entry point to manage lifetime of maps
 */
class MacroPointMapManager {
  // -- consts and 'read-only' fields --
  #ROOT_DIV_ID;

  #HIDE_MAP_CSS_CLASS = "map-hide-map";

  #SHOW_MAP_CSS_CLASS = "map-show-map";

  /**
   * Hook to call back into blazor
   */
  #DOTNET_CALLBACK;

  // -- (end) consts and 'read-only' fields --

  // -- private fields --
  #mapDictionary = {};
  #showTraffic = false;
  #showWeather = false;
  #activeMapKey = undefined;
  // --= (end private fields)

  // -- public API 'interface' methods
  removeMap(mapKeyToRemove, mapKeyToFocus) {
    // show new map
    if (mapKeyToFocus) {
      this.setFocus(mapKeyToFocus);
    }
    // remove map in the background
    const existingMap = this.#findMap(mapKeyToRemove);
    if (existingMap) {
      var currentMap =
        typeof existingMap.map.getMap != "undefined"
          ? existingMap.map.getMap()
          : null;
      var Zoom = currentMap != null ? currentMap.getZoom() : 0.0;
      var LngLatBounds = currentMap != null ? currentMap.getBounds() : null;
      existingMap.map.remove(); // call map instance to remove itself from DOM
      delete this.#mapDictionary[mapKeyToRemove];
      return {
        Zoom,
        LngLatBounds,
      };
    }
  }

  showMap(mapKey) {
    const map = this.#mapDictionary[mapKey];
    if (map) {
      const divId = map.divHostId;
      this.hideMap(this.#activeMapKey); // hide existing map
      if (divId) {
        $(`#${divId}`)
          .removeClass(this.#HIDE_MAP_CSS_CLASS)
          .addClass(this.#SHOW_MAP_CSS_CLASS);
        this.#activeMapKey = mapKey;
        // console.log(`active map set to ${this.#activeMapKey}`);
      }
      //  map.redraw();
    }
  }

  hideMap(mapKey) {
    const map = this.#mapDictionary[mapKey];
    if (map) {
      const divId = map.divHostId;
      if (divId) {
        // console.log(`hiding map ${mapKey}`);
        $(`#${divId}`)
          .removeClass(this.#SHOW_MAP_CSS_CLASS)
          .addClass(this.#HIDE_MAP_CSS_CLASS);
      }
    }
  }

  /**
   * Bring a map into focus, hiding the existing map (if necessary)
   * @param {string} mapKey Guid of map to bring into focus
   */
  setFocus(mapKey) {
    if (mapKey === this.#activeMapKey) return; // no need to do additional showing/hiding
    // hide active map
    if (this.#activeMapKey) {
      this.hideMap(this.#activeMapKey);
    }

    // show requested map
    this.#activeMapKey = mapKey;
    this.showMap(this.#activeMapKey);
  }

  removeAll() {
    if (!this.#mapDictionary) return;
    // ReSharper disable once MissingHasOwnPropertyInForeach
    // eslint-disable-next-line no-restricted-syntax, guard-for-in
    for (const mapKey in this.#mapDictionary) {
      this.removeMap(mapKey);
    }
  }

  removePopups() {
    if (!this.#mapDictionary) return;
    // ReSharper disable once MissingHasOwnPropertyInForeach
    // eslint-disable-next-line no-restricted-syntax, guard-for-in
    for (const mapKey in this.#mapDictionary) {
      if (mapKey) {
        var mapEntry = this.#findMap(mapKey);
        if (mapEntry?.map && typeof mapEntry.map.removePopups != "undefined") {
          mapEntry.map.removePopups();
        }
      }
    }
  }

  /**
   * Creates (optional) a named map then sets the data on a specific map.  Brings the map into focus.
   *
   * @param {CTactiveOptions} activeOptions Options populated with values suitable for the given map type.  Not all fields will be set (optional if not creating a map)
   * @param {string} mapKey Unique identifier for this instance of a map (optional if not creating a map)
   * @param {"locations" | "route" } mapType The type of map CTMapManager.MAPTYPE_LOCATIONS or CTMapManager.MAPTYPE_ROUTE values (optional if not creating a map)
   * @param {string} serializedLocationData to set on the map. JSONSerialized string. (optional)
   */
  setData(mapOptions, mapKey, mapType, serializedLocationData) {
    try {
      let existingMap = this.#findMap(mapKey);

      // map doesn't exist so create one on the fly
      if (!existingMap) {
        if (!mapOptions || !mapKey || !mapType) {
          throw new Error(
            `Could not find registered map '${mapKey}' and can not create map from empty construction parameters`
          );
        }
        existingMap = this.createMap(
          mapOptions,
          mapKey,
          mapType,
          serializedLocationData
        );
        return;
      }

      let data;

      if (serializedLocationData) {
        try {
          data = JSON.parse(serializedLocationData);
        } catch (e) {
          console.error(
            `Unable to deserialize location data.  ${mapKey} map will reset to default internal data.`
          );
          console.error(serializedLocationData);
        }
      } else {
        // no data so maps *should* result in empty data set. This is possible if there is no data to actually display for map
      }

      // console.debug(`setting data on map ${mapKey}`);
      //  this.setFocus(mapKey);
      existingMap.map.setData(data);

      // update traffic and weather overlays (just in case they might be out of sync)
      if (typeof mapOptions.showTraffic !== "undefined") {
        existingMap.map.setTraffic(mapOptions.showTraffic);
      }
      if (typeof mapOptions.showWeather !== "undefined") {
        existingMap.map.setWeather(mapOptions.showWeather);
      }
    } catch (e) {
      console.error(e);
    }
  }

  /**
   * Create an instance of either kind of map
   *
   * @param {CTMapOptions} mapOptions Options populated with values suitable for the given map type.  Not all fields will be set
   * @param {string} mapKey Unique identifier for this instance of a map
   * @param {"pingmap" | "loadmap" } mapType
   * @param {string} serializedMapData data that will initialize map source (optional)
   * @param {string} pinnedLoadUids list of UIDs currently pinned
   * @return {"MacroPointPingMap" | "MacroPointLoadMap"} One of the newly created maps
   */
  createMap(mapOptions, mapKey, mapType, serializedMapData, pinnedLoadUids) {
    // console.debug(`Creating map ${mapKey}`);
    // eslint-disable-next-line no-param-reassign
    const activeOptions = mapOptions ?? {};
    // load options (some options may not be applicable to all maps)
    activeOptions.colors = MacroPointMapManager.#loadColors(mapType);
    activeOptions.divHostId = this.#createDivIfNotExists(mapKey);
    activeOptions.mapStyle ??= MacroPointMapConst.MAP_STYLES.Transportation;
    //console.log("active options")
    activeOptions.mapKey = mapKey;
    activeOptions.pinnedLoads = pinnedLoadUids;

    if (typeof activeOptions.showTraffic === "undefined") {
      activeOptions.showTraffic = this.#showTraffic;
    }
    if (typeof activeOptions.showWeather === "undefined") {
      activeOptions.showWeather = this.#showWeather;
    }
    console.log(`creating ${mapType} map ${mapKey}`);
    activeOptions.paramKeys ??= MacroPointMapConst.PROPERTY_KEYS;
    //console.dir(JSON.stringify(activeOptions));
    //debugger;
    let mapData;
    try {
      if (serializedMapData) {
        mapData = JSON.parse(serializedMapData);
      }
    } catch (e) {
      // ignore deserialization error so maps will use their internal default dataset instead
      console.error(
        `Error parsing serialized map data. Using empty data. ${e}`
      );
      throw Error(e); // TODO set empty GeoJsonObject
    }

    switch (mapType) {
      case MacroPointMapConst.MAP_TYPES.pingMap:
        activeOptions.centerCoordinates ??=
          MacroPointMapConst.DEFAULT_CENTER_COORDINATES;
        activeOptions.blazorCallBack = this.#DOTNET_CALLBACK;

        var newMap = new MacroPointPingMap(activeOptions, mapData);
        this.#mapDictionary[mapKey] = {
          map: newMap,
          divHostId: activeOptions.divHostId,
          activeOptions: activeOptions,
          mapType: MacroPointMapConst.MAP_TYPES.pingMap,
        };
        break;
      case MacroPointMapConst.MAP_TYPES.loadMap:
        this.#mapDictionary[mapKey] = {
          map: new MacroPointLoadMap(activeOptions, mapData),
          divHostId: activeOptions.divHostId,
          activeOptions: activeOptions,
          mapType: MacroPointMapConst.MAP_TYPES.loadMap,
        };
        break;
      default:
        throw new Error(`Unable to create a map of mapType=${mapType}`);
    }

    this.setFocus(mapKey);
    //return this.#mapDictionary[mapKey];
  }

  /**
   * sets the weather radar overlay on all maps per RD 22-08-15
   *
   * @param {boolean} weatherFlag
   */
  setWeather(weatherFlag) {
    if (!this.#mapDictionary) return;
    if (typeof weatherFlag === "undefined") {
      weatherFlag = false;
    }
    this.#showWeather = weatherFlag;
    // ReSharper disable once MissingHasOwnPropertyInForeach
    // eslint-disable-next-line no-restricted-syntax, guard-for-in
    for (const mapKey in this.#mapDictionary) {
      if (mapKey) {
        var mapEntry = this.#findMap(mapKey);
        if (mapEntry?.map) {
          mapEntry.map.setWeather(this.#showWeather);
        }
      }
    }
  }

  /**
   * sets the traffic overlay on all mamps per RD 22-08-15
   *
   * @param {boolean} tafficFlag show/hide traffic. If undefined, toggle traffic
   */
  setTraffic(trafficFlag) {
    if (!this.#mapDictionary) return;
    if (typeof trafficFlag === "undefined") {
      trafficFlag = false;
    }
    this.#showTraffic = trafficFlag;
    // ReSharper disable once MissingHasOwnPropertyInForeach
    // eslint-disable-next-line no-restricted-syntax, guard-for-in
    for (const mapKey in this.#mapDictionary) {
      if (mapKey) {
        var mapEntry = this.#findMap(mapKey);
        if (mapEntry?.map) {
          mapEntry.map.setTraffic(this.#showTraffic);
        }
      }
    }
  }

  setDeclustering(isDeclustering) {
    if (!this.#mapDictionary) return;
    if (typeof isDeclustering === "undefined") {
      isDeclustering = false;
    }
    // ReSharper disable once MissingHasOwnPropertyInForeach
    // eslint-disable-next-line no-restricted-syntax, guard-for-in
    for (const mapKey in this.#mapDictionary) {
      if (mapKey) {
        var mapEntry = this.#findMap(mapKey);
        if (mapEntry?.map) {
          mapEntry.map.setDeclustering(isDeclustering);
        }
      }
    }
  }

  isClustered() {
    if (!this.#mapDictionary) return;
    return !MpQuickGrid.isMapDeclustered;
  }

  setPinState(mapKey, shipmentUid, isPinned) {
    for (const existingMapKey in this.#mapDictionary) {
      if (existingMapKey) {
        var mapEntry = this.#findMap(existingMapKey);
        if (mapEntry?.map) {
          if (mapEntry.mapType == MacroPointMapConst.MAP_TYPES.pingMap) {
            mapEntry.map.setPinState(shipmentUid, isPinned);
          }
        }
      }
    }
  }

  /**
   * @param {string} mapKey name of map to change style
   * @param {string} mapStyleName one of the well known MacroPoint map style names
   */
  setMapStyle(mapKey, mapStyleName) {
    const mapEntry = this.#findMap(mapKey);
    if (!mapEntry) {
      throw new Error(`Unable to find map with key '${mapKey}'`);
    }
    if (typeof mapStyleName !== "undefined") {
      mapEntry.map.setStyle(mapStyleName);
    } else {
      mapEntry.map.setStyle(MacroPointMapConst.MAP_TYPES.Transportation);
    }
  }
  // --- (end) public API 'interface' methods ---

  // -- private methods ---
  static #loadColors(maptype) {
    switch (maptype) {
      case MacroPointMapConst.MAP_TYPES.pingMap:
        return {
          clusterMarkerFill: MacroPointMapManager.#getCssVar(
            "map-ping-cluster-marker-fill-color"
          ),
          clusterMarkerBorder: MacroPointMapManager.#getCssVar(
            "map-ping-cluster-marker-border-color"
          ),
          clusterMarkerTextColor: MacroPointMapManager.#getCssVar(
            "map-ping-cluster-marker-text-color"
          ),
          nonclusteredMarkerBorderColor:
            MacroPointMapManager.#getCssVar(
              "map-ping-nonclustered-marker-border-color"
            ) ?? MacroPointMapConst.NONCLUSTERED_MARKER_BORDER_COLOR,
          delivery: {
            ontime: MacroPointMapManager.#getCssVar(
              "mp-deliverystatus-ontime-color"
            ),
            offline: MacroPointMapManager.#getCssVar(
              "mp-deliverystatus-offline-color"
            ),
            undeliverable: MacroPointMapManager.#getCssVar(
              "mp-deliverystatus-undeliverable-color"
            ),
            other: MacroPointMapManager.#getCssVar(
              "mp-deliverystatus-other-color"
            ),
            behind: MacroPointMapManager.#getCssVar(
              "mp-deliverystatus-behind-color"
            ),
            delivered: MacroPointMapManager.#getCssVar(
              "mp-deliverystatus-delivered-color"
            ),
            default:
              MacroPointMapManager.#getCssVar(
                "mp-deliverystatus-default-color"
              ) ?? "black",
          },
        };
      case MacroPointMapConst.MAP_TYPES.loadMap:
        return {
          stopFillColor: MacroPointMapManager.#getCssVar(
            "map-load-stop-fill-color"
          ),
          stopTextColor: MacroPointMapManager.#getCssVar(
            "map-load-stop-text-color"
          ),
          routeColor: MacroPointMapManager.#getCssVar("map-load-route-color"),
          locationFillColor: MacroPointMapManager.#getCssVar(
            "map-load-location-fill-color"
          ),
          locationBorderColor: MacroPointMapManager.#getCssVar(
            "map-load-location-border-color"
          ),
          lastLocationColor:
            MacroPointMapManager.#getCssVar("map-load-pin-color"),
        };
      default:
        throw new Error(`Unable to find colors for maptype ${maptype}`);
    }
  }

  static #getCssVar(cssVarName) {
    const foundValue = window
      .getComputedStyle(document.documentElement)
      .getPropertyValue(`--${cssVarName}`);
    return foundValue.trim();
  }

  #findMap(mapKey) {
    const foundMap = this.#mapDictionary[mapKey];
    return foundMap;
  }

  /**
   * Create <div> to contain map if div does not already exist
   * @param {string} mapKey - usually the TAB uid that is sourcing a map
   * @return {string} - id of div for map container
   */
  #createDivIfNotExists(mapKey) {
    const dataKey = "data-mp-map-key";
    const containerIdentifier = this.#ROOT_DIV_ID;
    console.log(`ROOT_DIV_ID ${this.#ROOT_DIV_ID}`);

    const containerElement = $(`#${containerIdentifier}`); // document.getElementById(`#${containerIdentifier}`);
    if (!containerElement.length) {
      throw new Error(
        `Unable to find required map container element '#${containerIdentifier}'`
      );
    }

    const activeMapKey = `map-${mapKey}`;

    let mapElement = $(`#${activeMapKey}`);
    if (!mapElement.length) {
      containerElement.append(
        `<div id="${activeMapKey}" ${dataKey}="${activeMapKey}" class="mp-map-instance"></div>`
      );
      mapElement = $(`#${activeMapKey}`);
      if (!mapElement.length) {
        throw new Error(`Unable to create child map element ${activeMapKey}`);
      }
    }
    return mapElement[0].id;
  }

  // (end) -- private methods ---

  // --- public static methods ---

  /**
   * Set up global variables necessary before using Trimble maps or other global settings
   *
   * @param {string} apiKey API key required to use TrimbleMaps (required)
   * @param {boolean} prewarm https://developer.trimblemaps.com/maps-sdk/api/#prewarm (optional)
   * @param {string} rootDivId ID of div that will be hosting all created maps. Usually set in the Blazor component (required)
   */
  initialize(apiKey, rootDivId, prewarm, dotNetCallBack) {
    console.log("initalizing map manager");
    if (!rootDivId) {
      throw new Error("rootDivId is a required field");
    }

    if (!apiKey) throw new Error("Required parameter 'apiKey' is not found");
    // eslint-disable-next-line no-undef
    TrimbleMaps.APIKey = apiKey;
    //if (typeof (prewarm) === 'undefined' || prewarm) {
    //    TrimbleMaps.prewarm();
    //}

    this.#DOTNET_CALLBACK = dotNetCallBack;
    if (!rootDivId) {
      // this div's existance is checked during map creation time so MapManager can be initialized without the existance of div in the DOM
      throw new Error("Required parameter 'rootDivId' is is not found");
    }

    var element = `#${rootDivId}`;
    if (!element?.length) {
      throw new Error(`Required div id="${rootDivId}" is missing from DOM`);
    }

    this.#ROOT_DIV_ID = rootDivId;
    this.#mapDictionary = {};
  }

  /**
   *
   * @returns Compressed guid string.  Not interchangable with industry standard UUID
   */
  static generateUUID() {
    // Public Domain/MIT
    let d = new Date().getTime(); // Timestamp
    let d2 =
      (typeof performance !== "undefined" &&
        performance.now &&
        performance.now() * 1000) ||
      0; // Time in microseconds since page-load or 0 if unsupported
    let templateString = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx";
    templateString = "xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx";
    const fullUid = templateString.replace(/[xy]/g, (c) => {
      let r = Math.random() * 16; // random number between 0 and 16
      if (d > 0) {
        // Use timestamp until depleted
        r = (d + r) % 16 | 0;
        d = Math.floor(d / 16);
      } else {
        // Use microseconds since page-load if supported
        r = (d2 + r) % 16 | 0;
        d2 = Math.floor(d2 / 16);
      }
      return (c === "x" ? r : (r & 0x3) | 0x8).toString(16);
    });

    return fullUid;
  }

  // --- (end) public static methods ---
}

// register a single map manager in the global window namespace
window.macropointMapManager = new MacroPointMapManager();
