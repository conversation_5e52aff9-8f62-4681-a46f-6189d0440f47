import { oweColors } from "./Colors";

export const oweTheme = {
  colors: {
    primary: oweColors.primary[100],
    onPrimary: "rgb(255, 255, 255)",
    primaryContainer: "rgb(214, 227, 255)",
    onPrimaryContainer: "rgb(0, 27, 62)",
    secondary: oweColors.primary[200],
    onSecondary: "rgb(255, 255, 255)",
    secondaryContainer: "rgb(216, 226, 255)",
    onSecondaryContainer: "rgb(0, 26, 65)",
    tertiary: "rgb(111, 85, 117)",
    onTertiary: "rgb(255, 255, 255)",
    tertiaryContainer: "rgb(249, 216, 253)",
    onTertiaryContainer: "rgb(40, 19, 46)",
    error: "rgb(186, 26, 26)",
    onError: "rgb(255, 255, 255)",
    errorContainer: "rgb(255, 218, 214)",
    onErrorContainer: "rgb(65, 0, 2)",
    background: "rgb(253, 251, 255)",
    onBackground: "rgb(26, 27, 30)",
    surface: "rgb(253, 251, 255)",
    onSurface: "rgb(26, 27, 30)",
    surfaceVariant: "rgb(224, 226, 236)",
    onSurfaceVariant: "rgb(68, 71, 78)",
    outline: "rgb(116, 119, 127)",
    outlineVariant: "rgb(196, 198, 208)",
    shadow: "rgb(0, 0, 0)",
    scrim: "rgb(0, 0, 0)",
    inverseSurface: "rgb(47, 48, 51)",
    inverseOnSurface: "rgb(241, 240, 244)",
    inversePrimary: "rgb(170, 199, 255)",
    elevation: {
      level0: "transparent",
      level1: "rgb(242, 243, 251)",
      level2: "rgb(236, 238, 248)",
      level3: "rgb(230, 234, 245)",
      level4: "rgb(228, 232, 244)",
      level5: "rgb(224, 229, 243)",
    },
    surfaceDisabled: "rgba(26, 27, 30, 0.12)",
    onSurfaceDisabled: "rgba(26, 27, 30, 0.38)",
    backdrop: "rgba(45, 48, 56, 0.4)",
    accent: oweColors.accent,
    onAccent: "rgb(255, 255, 255)",
    accentContainer: "rgb(255, 219, 206)",
    onAccentContainer: "rgb(55, 14, 0)",
  },
};