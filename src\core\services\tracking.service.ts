import { StorageKeys } from '../models/common';
import { LoadStatusType, MarketLoad } from '../models/load-market.model';
import { Tracking, TrackPoint } from '../models/tracking.model';
import { storeInCache, supabaseUrl, supabase } from "@utils/app.settings";
import * as ImagePicker from 'expo-image-picker';
import * as Sentry from '@sentry/react-native';

export class TrackingService {
  static async get(loadId: string): Promise<{ data?: Tracking; error?: string }> {
    const { data, error } = await supabase.from('trackings').select('*').eq('load', loadId);

    return {
      data: data?.at(0),
      error: error?.message,
    };
  }

  static async getById(id: string): Promise<{ data?: Tracking; error?: string }> {
    if (!id) return { data: undefined, error: 'No id provided' };

    const { data, error } = await supabase.from('trackings').select('*').eq('id', id);

    if (data) {
      await storeInCache(StorageKeys.TRACKING, data[0]);
    }
    return {
      data: data?.at(0),
      error: error?.message,
    };
  }

  static async updateTracking(
    payload: Tracking,
    driverName: string
  ): Promise<{ data?: Tracking; error?: string }> {
    const { id, ...item } = payload;
    item.updatedAt = new Date().toISOString();
    item.updatedName = driverName;
    const res = await supabase.from('trackings').update(item).eq('id', id).select('*');

    if (res.data) {
      await storeInCache(StorageKeys.TRACKING, payload);
    }
    return { data: res.data?.at(0) as Tracking, error: res.error?.message };
  }

  static async addDocuments(
    load: MarketLoad,
    documents: ImagePicker.ImagePickerAsset[],
    comment?: string
  ) {
    const urls = await TrackingService.upload(documents);

    if (!urls.length) return;
    const items = urls.map((url, i) => ({
      id: '',
      name: comment
        ? 'Additional Proof'
        : (load.status === LoadStatusType.IN_TRANSIT ? 'Proof of Pickup ' : 'Proof of Delivery ') +
          (i + 1),
      url: url,
      description: comment
        ? comment
        : i +
          1 +
          (load.status === LoadStatusType.IN_TRANSIT
            ? ' Driver provided Proof of Pickup'
            : ' Driver provided Proof of Delivery'),
      type: 'Mobile App',
      accountType: 'market_loads',
      accountId: load.id,
    }));
    await supabase
      .from('documents')
      .insert(
        items.map((s) => {
          const { id, ...data } = s;
          return {
            ...data,
            createdBy: load.createdBy,
            createdName: load.driver.name,
          };
        })
      )
      .select();
  }

  static async upload(files: ImagePicker.ImagePickerAsset[]) {
    try {
      const res = await Promise.all(
        files.map(async (file) => {
          const filename = file.uri.split('/').pop();
          // Infer the type of the image
          const match = /\.(\w+)$/.exec(filename!);
          const type = match ? `image/${match[1]}` : `image`;
          const prefix = 'mobile_pf_';
          const formData = new FormData();
          formData.append('photo', { uri: file.uri, name: prefix + filename, type } as any);

          const res = await supabase.storage
            .from('document-admin')
            .upload(prefix + filename!, formData);
          return res.data?.fullPath;
        })
      );
      return res
        .map((x) => (x ? `${supabaseUrl}/storage/v1/object/public/${x}` : null))
        .filter((v): v is string => !!v);
    } catch (error) {
      Sentry.captureException(error);
    }
    return [];
  }

  static async updateTracksCache(pins: TrackPoint[]) {
    const filterDataByDateAndProximity = (
      data: TrackPoint[],
      minuteSeparator: number
    ): TrackPoint[] => {
      data.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

      const filteredData: TrackPoint[] = [];

      for (const location of data) {
        if (!filteredData.length) {
          filteredData.push(location);
        } else {
          const lastFilteredLocation = filteredData[filteredData.length - 1];

          if (isWithinRadius(location, lastFilteredLocation, 40)) {
            if (new Date(location.date).getTime() < new Date(lastFilteredLocation.date).getTime()) {
              filteredData[filteredData.length - 1] = location;
            }
          } else {
            const durationInMinutes =
              (new Date(location.date).getTime() - new Date(lastFilteredLocation.date).getTime()) /
              1000 /
              60;

            if (durationInMinutes >= minuteSeparator) {
              filteredData.push(location);
            }
          }
        }
      }

      return filteredData;
    };

    const isWithinRadius = (
      location1: { latitude: number; longitude: number },
      location2: { latitude: number; longitude: number },
      radiusInMeters: number
    ): boolean => {
      const EARTH_RADIUS_IN_METERS = 6371e3;
      const lat1 = (location1.latitude * Math.PI) / 180;
      const lon1 = (location1.longitude * Math.PI) / 180;
      const lat2 = (location2.latitude * Math.PI) / 180;
      const lon2 = (location2.longitude * Math.PI) / 180;

      const dLat = lat2 - lat1;
      const dLon = lon2 - lon1;

      const a = Math.sin(dLat / 2) ** 2 + Math.cos(lat1) * Math.cos(lat2) * Math.sin(dLon / 2) ** 2;
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

      const distanceInMeters = EARTH_RADIUS_IN_METERS * c;

      return distanceInMeters <= radiusInMeters;
    };
    await storeInCache(StorageKeys.TRACKS, filterDataByDateAndProximity(pins, 2));
  }
}
