// import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';
import Storage from 'expo-sqlite/kv-store';
import { TrackPoint } from '@core/models/tracking.model';

export const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

export const supabase = createClient(supabaseUrl!, supabaseAnonKey!);

export const storeInCache = async (key: string, value: any) => {
  try {
    const jsonValue = JSON.stringify(value);
    await Storage.removeItem(key);
    await Storage.setItem(key, jsonValue);
  } catch (e) {
    console.log('cannot store in cache ', e);
  }
};

export const deleteCache = async (key: string) => {
  await Storage.removeItem(key);
};

export const getFromCache = async <T>(key: string) => {
  try {
    const jsonValue = await Storage.getItem(key); // storage.getString(key);
    return !!jsonValue ? JSON.parse(jsonValue) as T : null;
  } catch (e) {
    return null;
  }
};

export const flushCache = async () => {
  await Storage.clear(); // storage.clearAll();
};

export const combineLists = (list1: TrackPoint[], list2: TrackPoint[]) => {
  const seen = new Set();
  return [...list1, ...list2].filter(
    (x) =>
      !seen.has(new Date(x.date).getTime()) &&
      seen.add(new Date(x.date).getTime())
  );
};

const toDate = (value: any) => {
  if (typeof value === 'string') {
    return new Date(value);
  }
  return value instanceof Date ? value : new Date(value);
};

export const removeDuplicateDates = (items: TrackPoint[]): TrackPoint[] => {
  const getDateKey = (date: Date): string => {
    const dateItem = toDate(date);
    //
    const minutes = Math.floor(dateItem.getTime() / 100000); // 60000 is the number of milliseconds in 1 minute
    return `${dateItem.getFullYear()}-${padZero(dateItem.getMonth() + 1)}-${padZero(
      dateItem.getDate()
    )}T${padZero(dateItem.getHours())}:${padZero(minutes)}:00.000Z`;
  };

  const padZero = (num: number): string => {
    return (num < 10 ? '0' : '') + num;
  };
  const uniqueDates = new Map<string, TrackPoint>();

  items.forEach((item) => {
    const dateKey = getDateKey(new Date(item.date));
    if (!uniqueDates.has(dateKey)) {
      uniqueDates.set(dateKey, item);
    }
  });

  return Array.from(uniqueDates.values());
};
export function getId(item: string | any): string {
  if (typeof item === 'string') {
    return item;
  }
  return item?.id;
}

export const tryPromise = <T>(input: Promise<T>) => {
  return input
    .then((res) => [undefined, res] as [undefined, T])
    .catch((err) => [err, undefined] as [Error, undefined]);
};

export function generateUUID() {
  // Public Domain/MIT
  var d = new Date().getTime(); //Timestamp
  var d2 = (typeof performance !== 'undefined' && performance.now && performance.now() * 1000) || 0; //Time in microseconds since page-load or 0 if unsupported
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = Math.random() * 16; //random number between 0 and 16
    if (d > 0) {
      r = (d + r) % 16 | 0;
      d = Math.floor(d / 16);
    } else {
      r = (d2 + r) % 16 | 0;
      d2 = Math.floor(d2 / 16);
    }
    return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
}