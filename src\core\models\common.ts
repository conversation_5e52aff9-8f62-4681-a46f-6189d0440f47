import { Tracking } from './tracking.model';
import { MarketLoad } from './load-market.model';

export type LiveData = {
  load?: MarketLoad;
  tracking?: Tracking;
  locationGranted: boolean;
  type: Strategy;
};

export enum Strategy {
  NONE = 0,
  SIGIN = 1,
  LIVE = 2,
  DONE = 3,
}

export enum StorageKeys {
  USER = '_user',
  LOAD = '_shipment_load',
  TRACKING = '_tracking',
  TRACKS = '_tracks_',
}
