import React, { useEffect, useState } from "react";
import { View, Image, SafeAreaView, Linking, Platform } from "react-native";
import {
  Appbar,
  Text,
  FAB,
  ActivityIndicator,
  MD2Colors,
  Badge,
} from "react-native-paper";
import * as Location from "expo-location";
import { oweColors } from "@utils/Colors";
import {
  enableLiveLocationTracking,
  disableLiveLocationTracking,
  isShipmentComplete,
} from "@core/services/live.task";
import { StorageKeys, Strategy } from "@core/models/common";
import { ShipmentService } from "@core/services/shipment.service";
import { TrackingService } from "@core/services/tracking.service";
import { LoadStatusType, MarketLoad } from "@core/models/load-market.model";
import { Messaging, Tracking } from "@core/models/tracking.model";
import { getFromCache } from "@utils/app.settings";
import * as Sentry from "@sentry/react-native";
import { AddInfo, BolInfo } from "@core/models/bolInfo.model";
import { useStrategy } from "@/src/context/main.context";
import LoadView from "@/src/components/loadView";
import ChatView from "@/src/components/chat";
import { useRouter } from "expo-router";

const HomeScreen = () => {
  const { data, setStrategy, clearStrategy } = useStrategy();
  const [isLoading, setIsLoading] = useState(false);
  const [openChat, setOpenChat] = useState(false);
  const [messaging, setMessaging] = useState<Messaging | undefined>(undefined);
  const router = useRouter();

  const svc = new ShipmentService();
  let load = data?.load;
  const userName = data?.load?.driver?.name || "--|--";

  useEffect(() => {
    if (!data?.load?.driver) {
      router.push("/signIn");
      return;
    }
  }, [data]);

  useEffect(() => {
    if (!data?.load) {
      router.push("/signIn");
      return;
    }

    const subscription = svc
      .subscribe(load?.id!, async (res) => {
        console.log("live change received::: ", res.status);

        // Check if shipment is completed and stop location tracking
        if (isShipmentComplete(res.status)) {
          console.log(
            `Shipment completed with status: ${res.status}, stopping location tracking`
          );
          await disableLiveLocationTracking();

          if (res.status === LoadStatusType.DELIVERED) {
            setStrategy({
              load: res,
              tracking: data.tracking,
              locationGranted: true,
              type: Strategy.DONE,
            });
            router.push("/complete");
            return;
          }
        }

        TrackingService.getById(data.tracking?.id!).finally(() => {
          homeRedirect(res, data.tracking);
        });
        load = res;
      })
      .subscribe();

    const messageSub = svc
      .subscribeMessage(load?.id!, (res) => {
        const item = (res?.messages || []).reduce((latest, current) => {
          return new Date(current.date) > new Date(latest.date)
            ? current
            : latest;
        }, res.messages[0]);
        setMessaging(res);
      })
      .subscribe();

    checkBackgroundPermissions();
    if (load) {
      // Only start location tracking for incomplete shipments
      if (!isShipmentComplete(load.status)) {
        console.log(
          `Starting location tracking for shipment with status: ${load.status}`
        );
        enableLiveLocationTracking(load);
      } else {
        console.log(
          `Shipment already completed with status: ${load.status}, not starting location tracking`
        );
      }
    }

    return () => {
      subscription.unsubscribe();
      messageSub.unsubscribe();
    };
  }, []);

  const checkBackgroundPermissions = async () => {
    const { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== "granted") {
      alert("Permission to access location was denied");
      return;
    }
    const backPermission = await Location.requestBackgroundPermissionsAsync();
    if (!backPermission.canAskAgain || backPermission.status === "denied") {
      Sentry.captureMessage("User has denied  Background location permission.");
      Linking.openSettings();
    }
  };
  const homeRedirect = async (load?: MarketLoad, tracking?: Tracking) => {
    if (!load || !tracking) {
      return;
    }
    setIsLoading(true);
    const trackingCache = await getFromCache<Tracking>(StorageKeys.TRACKING);
    setStrategy({
      load,
      tracking: tracking || trackingCache,
      locationGranted: true,
      type: Strategy.LIVE,
    });
    setIsLoading(false);
  };

  const onAction = async (formData: BolInfo) => {
    if (isLoading) {
      return;
    }
    if (!load) {
      return;
    }

    if (formData.id === 2) {
      setIsLoading(true);
      const res = await svc.pickupShipment({
        endpoint: load.shipment!.routes[0],
        load,
        tracking: data.tracking!,
        bolDetails: formData,
      });
      setStrategy({
        load: res.load,
        tracking: res.tracking,
        locationGranted: true,
        type: Strategy.LIVE,
      });
      setIsLoading(false);
    }
    if (formData.id === 3) {
      setIsLoading(true);
      const res = await svc.dropOffShipment({
        endpoint: load.shipment!.routes[0],
        load,
        tracking: data.tracking!,
        bolDetails: formData,
      });
      setStrategy({
        load: res.load,
        tracking: res.tracking,
        locationGranted: true,
        type: Strategy.LIVE,
      });
      setIsLoading(false);
      return;
    }
  };

  const submitReview = async (dataInfo: AddInfo) => {
    if (isLoading) return;
    try {
      setIsLoading(true);
      await svc.additional(load!, data?.tracking!, dataInfo);
    } catch (error: any) {
      Sentry.captureException(error, {
        extra: {
          message: error.message || "Unable to submit additional information",
          loadId: data.load?.id,
          proId: data.load?.shipment.proId,
          tracking: data.tracking?.id,
          driver: data.load?.driver?.name,
        },
      });
    } finally {
      setIsLoading(false);
    }
  };

  const logOut = async () => {
    if (!data?.load) {
      router.push("/signIn");
      return;
    }
    setIsLoading(true);
    await clearStrategy();
    setIsLoading(false);
    router.push("/signIn");
  };

  const onChatClose = () => {
    setOpenChat(false);
    setMessaging(undefined);
  };

  const headerLogo = () => {
    return (
      <View
        style={{
          paddingLeft: 0,
          paddingRight: 5,
          flexDirection: "row",
          alignItems: "center",
          gap: 3,
        }}
      >
        <Image
          style={{ width: 50, height: 50 }}
          source={require("../assets/images/logo-black.png")}
        />
        <View>
          <Text style={{ color: "navy", fontSize: 15, fontWeight: 700 }}>
            1-Way Express
          </Text>
          <Text style={{ color: "white", fontSize: 20, fontWeight: 700 }}>
            Load Tracking
          </Text>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <Appbar.Header style={{ backgroundColor: oweColors.accent }}>
        <Appbar.Content title={headerLogo()} />
        <Appbar.Action icon="logout" onPress={() => logOut()} />
      </Appbar.Header>
      <View
        style={{
          paddingHorizontal: 10,
          width: "100%",
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
          gap: 10,
          paddingTop: 16,
        }}
      >
        <Text style={{ fontWeight: "500", fontSize: 16 }}>Welcome,</Text>
        <Text style={{ fontWeight: "800", fontSize: 20 }}>{userName}!</Text>
      </View>

      {!isLoading && data.load && (
        <View style={{ padding: 16 }}>
          <LoadView
            load={data.load}
            submitForm={submitReview}
            onAction={onAction}
          />
        </View>
      )}

      {isLoading && (
        <ActivityIndicator
          animating={true}
          size={"large"}
          color={MD2Colors.orange700}
        />
      )}
      <View
        style={{
          position: "absolute",
          margin: 16,
          right: 0,
          bottom: 0,
        }}
      >
        <FAB
          icon="message-reply-text"
          style={{
            backgroundColor: oweColors.accent,
          }}
          onPress={() => setOpenChat(true)}
        />
        <Badge
          visible={!!messaging?.messages?.length}
          size={24}
          style={{
            position: "absolute",
            top: -8,
            right: 0,
          }}
        >
          {messaging?.messages?.length}
        </Badge>
      </View>
      {openChat && Platform.OS !== "ios" && (
        <ChatView
          show={openChat}
          load={load!}
          liveMessage={messaging}
          close={() => onChatClose()}
        />
      )}
    </SafeAreaView>
  );
};

export default HomeScreen;
