/* eslint-disable max-classes-per-file */
class MacroPointMapConst {
    // --- const & 'readonly' variables initialized during constructor ---
    static get CLUSTER_MAX_ZOOM() { return 5; }

    static get CLUSTER_RADIUS() { return 40; }

    static get NONCLUSTERED_MARKER_RADIUS() { return 10; }

    static get NONCLUSTERED_MARKER_BORDER_STROKE() { return 1; }

    static get NONCLUSTERED_MARKER_BORDER_COLOR() { return 'white'; }

    /**
       * Border width to draw around each clustered maker
       */
    static get CLUSTERED_MARKER_STROKE() { return 1; }

    /**
       * Default color for cluster fill color if non supplied by caller
       */
    static get CLUSTERED_MARKER_FILL_COLOR() { return '#6D88A2'; }

    /**
       * Default border color for clustered marker if non supplied by caller
       */
    static get CLUSTERED_MARKER_BORDER_COLOR() { return '#0C2840'; }

    /**
       * Default color to draw the numbers on each clustered marker, if not supplied by caller
       */
    static get CLUSTERED_MARKER_TEXT_COLOR() { return '#FFF'; }

    static get STATUS_KEYS() {
        return {
            ontime: 'ontime',
            behind: 'behind',
            offline: 'offline',
            undeliverable: 'undeliverable',
            other: 'other',
        };
    }

    static get DEFAULT_STATUS() {
        return {
            ontime: { key: this.STATUS_KEYS.ontime, color: '#28a745' },
            behind: { key: this.STATUS_KEYS.behind, color: '#ffc107' },
            undeliverable: { key: this.STATUS_KEYS.undeliverable, color: 'firebrick' },
            offline: { key: this.STATUS_KEYS.offline, color: '#6f42c1' },
            other: { key: this.STATUS_KEYS.other, color: '#6D88A2' },
            default: 'black',
        };
    }

    static get MAP_TYPES() {
        return {
            pingMap: 'pingmap',
            loadMap: 'loadmap',
        };
    }

    static get MAP_STYLES() {
        return {
            Transportation: 'transportation',
            Satellite: 'satellite',
            Terrain: 'terrain'
        };
    }

    static get TRANSPORTATION() { return 'transportation'; }

    static get SATELLITE() { return 'satellite'; }

    static get DEFAULT_CENTER_COORDINATES() { return [-96, 35]; }

    static get DEFAULT_MAP_SYTLE() { return TrimbleMaps.Common.Style.TRANSPORTATION; }
    /**
       * keys on a feature's properties that will be used by popup and other consumers
       */
    static get PROPERTY_KEYS() {
        return {
            shipmentUid: 'sid',
            loadId: 'lid',
            carrierName: 'cn',

            // Status row header: 'Status' (localized)
            statusTitle: 'sl',

            // ontime, behind, undeliverable.  Used in logic and filtering
            loadStatusCode: 'dc',

            // localized version of load status
            loadStatusText: 'ds',

            // 13 hours behind
            deliveryStatusText: 'st',

            // 'Estimated arrival', 'Arrived'
            destTitle: 'dt',

            // destination addess to display
            destLocation: 'dl',

            // localized date to display
            destDisplayDate: 'dd',

            destTimeZone: 'dz',

            isPinned: 'ip',

            // keys for LoadMap popup
            pingDate: 'pingDate',
            pingTimezone: 'pingTimezone',
            pingLocation: 'pingLocation',
            coordinatesTitle: 'coordinatesTitle',
            coordinatesDisplay: 'coordinatesDisplay',
            showPin: 'sp'

        };
    }

    /**
       * Path to stop sign/route-stop image map uses to load this asset
       */
    static get ROUTE_STOP_IMAGE_URL() {
        return 'maps/images/stop.png';
    }
}

/**
 * Options used in the PingMap
 */
// eslint-disable-next-line no-unused-vars
class MacroPointPingMapOptions {
    /**
       * Initial zoom level for map (0-24, default: 4)
       */
    zoomLevel;

    /**
       * Existing HTML element that should host the instance of the map
       */
    hostDivId;

    /**
       * Where the map should center when opening.  Array or LngLat object (https://developer.trimblemaps.com/maps-sdk/api/#lnglatlike)
       */
    centerCoordinates = [-96, 35];

    /**
       * The UI style of the map to display when starting.  One of the MacroPointMapStyles constants. (default: MacroPointMapConst.MAP_STYLES.Transporation)
       */
    mapStyle = MacroPointMapConst.MAP_STYLES.Transporation;

    /**
       * Colors that are retrieved from currently active style sheets by MacroPointMapManager (e.g. Delivery status, clusters)
       */
    colors;
}

// eslint-disable-next-line no-unused-vars
class MacroPointLoadMapOptions {
    /**
       * Initial zoom level for map (0-24, default: 4)
       */
    zoomLevel;

    /**
        * Existing HTML element that should host the instance of the map
        */
    hostDivId;

    /**
       * The UI style of the map to display when starting.  One of the MacroPointMapStyles constants. (default: MacroPointMapConst.MAP_STYLES.Transporation)
       */
    mapStyle = MacroPointMapConst.MAP_STYLES.Transporation;

    /**
       * Colors that are retrieved from currently active style sheets by MacroPointMapManager
       */
    colors;
}
